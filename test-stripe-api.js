// Simple test script to verify Stripe API endpoints and debug user issues
// Run with: node test-stripe-api.js

const testUserId = '04a824f8-b0e1-70ce-2aba-b5abff83b7f0';
const testEmail = '<EMAIL>';
const baseUrl = 'http://localhost:3000';

async function debugUserIssues() {
  console.log('🔍 Debugging user lookup issues...\n');

  try {
    // Debug 1: Check DynamoDB health
    console.log('1️⃣ Checking DynamoDB connection health...');
    const healthResponse = await fetch(`${baseUrl}/api/debug/user?action=health`);
    const healthData = await healthResponse.json();

    console.log('Health Check Response:', healthResponse.status);
    console.log('Health Check Data:', JSON.stringify(healthData, null, 2));
    console.log('');

    if (!healthData.success) {
      console.log('❌ DynamoDB connection failed. Cannot proceed with user tests.');
      return false;
    }

    // Debug 2: Check auth context
    console.log('2️⃣ Checking auth debug info...');
    const authResponse = await fetch(`${baseUrl}/api/debug/auth?userId=${testUserId}`);
    const authData = await authResponse.json();

    console.log('Auth Debug Response:', authResponse.status);
    console.log('Auth Debug Data:', JSON.stringify(authData, null, 2));
    console.log('');

    // Debug 3: List all users in database
    console.log('3️⃣ Listing all users in database...');
    const listResponse = await fetch(`${baseUrl}/api/debug/user?action=list`);
    const listData = await listResponse.json();

    console.log('List Users Response:', listResponse.status);
    console.log('List Users Data:', JSON.stringify(listData, null, 2));
    console.log('');

    // Debug 4: Try to get specific user
    console.log('4️⃣ Trying to get specific user...');
    const getUserResponse = await fetch(`${baseUrl}/api/debug/user?userId=${testUserId}&action=get`);
    const getUserData = await getUserResponse.json();

    console.log('Get User Response:', getUserResponse.status);
    console.log('Get User Data:', JSON.stringify(getUserData, null, 2));
    console.log('');

    return getUserData.success;

  } catch (error) {
    console.error('❌ Debug failed with error:', error.message);
    return false;
  }
}

async function testStripeAPI() {
  console.log('🧪 Testing Stripe Connect API endpoints...\n');

  try {
    // Test 1: Check onboarding status (should return no account)
    console.log('1️⃣ Testing GET onboarding status...');
    const statusResponse = await fetch(`${baseUrl}/api/stripe/connect/onboarding-link?userId=${testUserId}`);
    const statusData = await statusResponse.json();

    console.log('Status Response:', statusResponse.status);
    console.log('Status Data:', JSON.stringify(statusData, null, 2));

    if (statusResponse.status === 200 && statusData.hasAccount === false) {
      console.log('✅ Status check passed - no account exists\n');
    } else if (statusResponse.status === 404) {
      console.log('⚠️ User not found - this is expected if user doesn\'t exist in database\n');
      return false; // User doesn't exist, can't continue with Stripe tests
    } else {
      console.log('❌ Unexpected status response\n');
    }

    // Test 2: Create Stripe account
    console.log('2️⃣ Testing POST create account...');
    const createResponse = await fetch(`${baseUrl}/api/stripe/connect/create-account`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        userId: testUserId,
        email: testEmail,
        country: 'US'
      })
    });
    
    const createData = await createResponse.json();
    console.log('Create Response:', createResponse.status);
    console.log('Create Data:', JSON.stringify(createData, null, 2));
    
    if (createResponse.status === 200 && createData.accountId) {
      console.log('✅ Account creation passed\n');
    } else if (createResponse.status === 404) {
      console.log('⚠️ User not found - this is expected if user doesn\'t exist in database\n');
    } else {
      console.log('❌ Account creation failed\n');
    }

    // Test 3: Check onboarding status again (should now have account)
    console.log('3️⃣ Testing GET onboarding status after account creation...');
    const statusResponse2 = await fetch(`${baseUrl}/api/stripe/connect/onboarding-link?userId=${testUserId}`);
    const statusData2 = await statusResponse2.json();
    
    console.log('Status Response 2:', statusResponse2.status);
    console.log('Status Data 2:', JSON.stringify(statusData2, null, 2));
    
    if (statusResponse2.status === 200 && statusData2.hasAccount === true) {
      console.log('✅ Status check after creation passed\n');
    } else {
      console.log('❌ Status check after creation failed\n');
    }

  } catch (error) {
    console.error('❌ Test failed with error:', error.message);
  }
}

// Check if server is running
async function checkServer() {
  try {
    const response = await fetch(`${baseUrl}/api/health`);
    if (response.ok) {
      console.log('✅ Server is running\n');
      return true;
    }
  } catch (error) {
    console.log('❌ Server is not running. Please start with: npm run dev\n');
    return false;
  }
}

async function createTestUser() {
  console.log('👤 Creating test user...\n');

  try {
    const createResponse = await fetch(`${baseUrl}/api/debug/user`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        action: 'create',
        userData: {
          id: testUserId,
          name: 'Test User',
          email: testEmail,
          role: 'FREELANCER'
        }
      })
    });

    const createData = await createResponse.json();
    console.log('Create User Response:', createResponse.status);
    console.log('Create User Data:', JSON.stringify(createData, null, 2));

    if (createResponse.status === 200 && createData.success) {
      console.log('✅ Test user created successfully\n');
      return true;
    } else {
      console.log('❌ Failed to create test user\n');
      return false;
    }

  } catch (error) {
    console.error('❌ Create user failed with error:', error.message);
    return false;
  }
}

async function main() {
  console.log('🚀 Starting comprehensive user and Stripe API tests...\n');

  const serverRunning = await checkServer();
  if (!serverRunning) {
    console.log('Please start the development server first:');
    console.log('npm run dev');
    return;
  }

  // Step 1: Debug user issues
  console.log('='.repeat(60));
  console.log('STEP 1: DEBUGGING USER LOOKUP ISSUES');
  console.log('='.repeat(60));
  const userExists = await debugUserIssues();

  // Step 2: Create test user if needed
  if (!userExists) {
    console.log('='.repeat(60));
    console.log('STEP 2: CREATING TEST USER');
    console.log('='.repeat(60));
    const userCreated = await createTestUser();

    if (!userCreated) {
      console.log('❌ Cannot proceed without a valid user. Please check the database connection and user service.');
      return;
    }
  }

  // Step 3: Test Stripe API
  console.log('='.repeat(60));
  console.log('STEP 3: TESTING STRIPE CONNECT API');
  console.log('='.repeat(60));
  await testStripeAPI();

  console.log('🏁 All tests completed!');
}

main().catch(console.error);
