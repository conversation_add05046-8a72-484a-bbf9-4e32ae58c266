// Simple test script to verify Stripe API endpoints
// Run with: node test-stripe-api.js

const testUserId = '04a824f8-b0e1-70ce-2aba-b5abff83b7f0';
const testEmail = '<EMAIL>';
const baseUrl = 'http://localhost:3000';

async function testStripeAPI() {
  console.log('🧪 Testing Stripe Connect API endpoints...\n');

  try {
    // Test 1: Check onboarding status (should return no account)
    console.log('1️⃣ Testing GET onboarding status...');
    const statusResponse = await fetch(`${baseUrl}/api/stripe/connect/onboarding-link?userId=${testUserId}`);
    const statusData = await statusResponse.json();
    
    console.log('Status Response:', statusResponse.status);
    console.log('Status Data:', JSON.stringify(statusData, null, 2));
    
    if (statusResponse.status === 200 && statusData.hasAccount === false) {
      console.log('✅ Status check passed - no account exists\n');
    } else if (statusResponse.status === 404) {
      console.log('⚠️ User not found - this is expected if user doesn\'t exist in database\n');
    } else {
      console.log('❌ Unexpected status response\n');
    }

    // Test 2: Create Stripe account
    console.log('2️⃣ Testing POST create account...');
    const createResponse = await fetch(`${baseUrl}/api/stripe/connect/create-account`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        userId: testUserId,
        email: testEmail,
        country: 'US'
      })
    });
    
    const createData = await createResponse.json();
    console.log('Create Response:', createResponse.status);
    console.log('Create Data:', JSON.stringify(createData, null, 2));
    
    if (createResponse.status === 200 && createData.accountId) {
      console.log('✅ Account creation passed\n');
    } else if (createResponse.status === 404) {
      console.log('⚠️ User not found - this is expected if user doesn\'t exist in database\n');
    } else {
      console.log('❌ Account creation failed\n');
    }

    // Test 3: Check onboarding status again (should now have account)
    console.log('3️⃣ Testing GET onboarding status after account creation...');
    const statusResponse2 = await fetch(`${baseUrl}/api/stripe/connect/onboarding-link?userId=${testUserId}`);
    const statusData2 = await statusResponse2.json();
    
    console.log('Status Response 2:', statusResponse2.status);
    console.log('Status Data 2:', JSON.stringify(statusData2, null, 2));
    
    if (statusResponse2.status === 200 && statusData2.hasAccount === true) {
      console.log('✅ Status check after creation passed\n');
    } else {
      console.log('❌ Status check after creation failed\n');
    }

  } catch (error) {
    console.error('❌ Test failed with error:', error.message);
  }
}

// Check if server is running
async function checkServer() {
  try {
    const response = await fetch(`${baseUrl}/api/health`);
    if (response.ok) {
      console.log('✅ Server is running\n');
      return true;
    }
  } catch (error) {
    console.log('❌ Server is not running. Please start with: npm run dev\n');
    return false;
  }
}

async function main() {
  console.log('🚀 Starting Stripe API tests...\n');
  
  const serverRunning = await checkServer();
  if (!serverRunning) {
    console.log('Please start the development server first:');
    console.log('npm run dev');
    return;
  }
  
  await testStripeAPI();
  console.log('🏁 Tests completed!');
}

main().catch(console.error);
