import { NextRequest, NextResponse } from 'next/server';
import { stripe } from '@/lib/stripe';
import { serverUserService } from '@/api/users/user.service.server';
import { StripeAccountStatus } from '@/types/features/payments/payment.types';

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { userId, email, country = 'US' } = body;

    console.log('Creating Stripe Express account for user:', { userId, email, country });

    if (!userId) {
      return NextResponse.json(
        { error: 'User ID is required.' },
        { status: 400 }
      );
    }

    if (!email) {
      return NextResponse.json(
        { error: 'Email is required.' },
        { status: 400 }
      );
    }

    // Check if user already has a Stripe account
    let user;
    try {
      user = await serverUserService.getUser(userId);
    } catch (error) {
      console.error('Error getting user:', error);
      console.error('User ID provided:', userId);
      console.error('User ID type:', typeof userId);
      console.error('User ID length:', userId?.length);

      // Check if it's a ResourceNotFoundError
      if (error instanceof Error && error.message.includes('not found')) {
        return NextResponse.json(
          {
            error: 'User not found',
            userId: userId,
            message: `No user found with ID: ${userId}`,
            suggestion: 'Please check if the user exists in the database or verify the user ID format',
            debugEndpoint: `/api/debug/user?userId=${userId}&action=get`
          },
          { status: 404 }
        );
      }

      return NextResponse.json(
        {
          error: 'Failed to retrieve user',
          details: error instanceof Error ? error.message : 'Unknown error'
        },
        { status: 500 }
      );
    }

    // Check if user was found
    if (!user) {
      return NextResponse.json(
        {
          error: 'User not found',
          userId: userId,
          message: `No user found with ID: ${userId}`,
          suggestion: 'Please check if the user exists in the database or verify the user ID format',
          debugEndpoint: `/api/debug/user?userId=${userId}&action=get`
        },
        { status: 404 }
      );
    }

    if (user.stripeAccountId) {
      return NextResponse.json(
        { error: 'User already has a Stripe account.' },
        { status: 400 }
      );
    }

    // Create Stripe Express account
    let account;
    try {
      account = await stripe.accounts.create({
        type: 'express',
        country: country,
        email: email,
        capabilities: {
          card_payments: { requested: true },
          transfers: { requested: true },
        },
        business_type: 'individual',
        metadata: {
          userId: userId,
          platform: 'myvillage-freelance'
        }
      });

      console.log('Stripe account created:', account.id);
    } catch (error) {
      console.error('Error creating Stripe account:', error);
      
      if (error instanceof Error) {
        return NextResponse.json(
          { 
            error: 'Failed to create Stripe account',
            details: error.message
          },
          { status: 500 }
        );
      }

      return NextResponse.json(
        { error: 'Failed to create Stripe account' },
        { status: 500 }
      );
    }

    // Update user with Stripe account information
    try {
      await serverUserService.updateUser({
        id: userId,
        stripeAccountId: account.id,
        stripeAccountStatus: StripeAccountStatus.PENDING,
        stripeAccountType: 'express',
        stripeChargesEnabled: account.charges_enabled || false,
        stripePayoutsEnabled: account.payouts_enabled || false,
        stripeDetailsSubmitted: account.details_submitted || false,
      });
    } catch (error) {
      console.error('Error updating user with Stripe account info:', error);
      
      // If user update fails, we should clean up the Stripe account
      try {
        await stripe.accounts.del(account.id);
        console.log('Cleaned up Stripe account due to user update failure');
      } catch (cleanupError) {
        console.error('Failed to cleanup Stripe account:', cleanupError);
      }
      
      return NextResponse.json(
        { 
          error: 'Failed to save Stripe account information',
          details: error instanceof Error ? error.message : 'Unknown error'
        },
        { status: 500 }
      );
    }

    return NextResponse.json({
      accountId: account.id,
      status: 'created',
      chargesEnabled: account.charges_enabled,
      payoutsEnabled: account.payouts_enabled,
      detailsSubmitted: account.details_submitted
    });

  } catch (error) {
    console.error('Unexpected error in create-account:', error);
    return NextResponse.json(
      { 
        error: 'An unexpected error occurred',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const userId = searchParams.get('userId');

    if (!userId) {
      return NextResponse.json(
        { error: 'User ID is required.' },
        { status: 400 }
      );
    }

    // Get user's Stripe account information
    let user;
    try {
      user = await serverUserService.getUser(userId);
    } catch (error) {
      console.error('Error getting user:', error);
      return NextResponse.json(
        { error: 'User not found' },
        { status: 404 }
      );
    }

    // Check if user was found
    if (!user) {
      return NextResponse.json({
        hasAccount: false,
        status: 'NOT_STARTED'
      });
    }

    if (!user.stripeAccountId) {
      return NextResponse.json({
        hasAccount: false,
        status: 'NOT_STARTED'
      });
    }

    // Get account details from Stripe
    let account;
    try {
      account = await stripe.accounts.retrieve(user.stripeAccountId);
    } catch (error) {
      console.error('Error retrieving Stripe account:', error);
      
      // If account doesn't exist in Stripe, clear it from user record
      if (error instanceof Error && error.message.includes('No such account')) {
        try {
          await serverUserService.updateUser({
            id: userId,
            stripeAccountId: null,
            stripeOnboardingComplete: false,
            stripeAccountStatus: null
          });
        } catch (updateError) {
          console.error('Error clearing invalid Stripe account ID:', updateError);
        }
        
        return NextResponse.json({
          hasAccount: false,
          status: 'NOT_STARTED'
        });
      }
      
      return NextResponse.json(
        { 
          error: 'Failed to retrieve Stripe account',
          details: error instanceof Error ? error.message : 'Unknown error'
        },
        { status: 500 }
      );
    }

    // Update user with latest account status
    try {
      await serverUserService.updateUser({
        id: userId,
        stripeChargesEnabled: account.charges_enabled || false,
        stripePayoutsEnabled: account.payouts_enabled || false,
        stripeDetailsSubmitted: account.details_submitted || false,
        stripeAccountStatus: account.charges_enabled && account.payouts_enabled ? StripeAccountStatus.ACTIVE : StripeAccountStatus.PENDING
      });
    } catch (error) {
      console.error('Error updating user Stripe status:', error);
      // Continue even if update fails, as the main functionality should still work
    }

    return NextResponse.json({
      hasAccount: true,
      accountId: account.id,
      status: account.charges_enabled && account.payouts_enabled ? 'ACTIVE' : 'PENDING',
      chargesEnabled: account.charges_enabled,
      payoutsEnabled: account.payouts_enabled,
      detailsSubmitted: account.details_submitted,
      requirements: account.requirements
    });

  } catch (error) {
    console.error('Unexpected error in create-account GET:', error);
    return NextResponse.json(
      { 
        error: 'An unexpected error occurred',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}
