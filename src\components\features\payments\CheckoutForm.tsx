'use client';

import React, { useState, useEffect } from 'react';
import Image from 'next/image';
import { useStripe, useElements, PaymentElement, Elements } from '@stripe/react-stripe-js';
import { Button } from '@/components/ui/Button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/Card';
import { Alert, AlertDescription } from '@/components/ui/Alert';
import { Badge } from '@/components/ui/Badge';
import { Icon } from '@/components/ui/Icon';
import { paymentService, CreatePaymentIntentRequest } from '@/api/payments';
import { PaymentError } from '@/types/features/payments/payment.types';
import { contractService } from '@/api/contracts/contract.service';
import { userService } from '@/api/users/user.service';
import { useStripeContext } from '@/providers/StripeProvider';

const isValidImageUrl = (url: string | undefined): boolean => {
  if (!url || typeof url !== 'string') return false;
  
  try {
    const urlObj = new URL(url);
    return urlObj.protocol === 'http:' || urlObj.protocol === 'https:';
  } catch {
    return false;
  }
};

interface CheckoutFormProps {
  contractId: string;
  amount: number;
  currency?: string;
  clientId: string;
  clientData?: {
    id: string;
    name: string;
    email: string;
    profilePhoto?: string;
  };
  onPaymentSuccess?: (paymentResult: any) => void;
  onPaymentError?: (error: PaymentError) => void;
  onCancel?: () => void;
  className?: string;
}

const CheckoutFormInternal: React.FC<CheckoutFormProps> = ({
  contractId,
  amount,
  currency = 'usd',
  clientData,
  onPaymentSuccess,
  onPaymentError,
  onCancel,
  className = '',
}) => {
  const stripe = useStripe();
  const elements = useElements();
  
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [paymentStatus, setPaymentStatus] = useState<'idle' | 'processing' | 'succeeded' | 'failed'>('idle');
  const [isElementsReady, setIsElementsReady] = useState(false);

  const handleSubmit = async (event: React.FormEvent) => {
    event.preventDefault();

    if (!stripe || !elements) {
      setError('Payment system not ready. Please try again.');
      return;
    }

    if (!isElementsReady) {
      setError('Payment form is still loading. Please wait a moment and try again.');
      return;
    }

    setIsLoading(true);
    setError(null);
    setPaymentStatus('processing');

    try {
      const { error: submitError } = await elements.submit();
      if (submitError) {
        throw new Error(submitError.message);
      }

      const { error: confirmError, paymentIntent } = await stripe.confirmPayment({
        elements,
        confirmParams: {
          return_url: `${window.location.origin}/payment/success?contract_id=${contractId}`,
        },
        redirect: 'if_required',
      });

      if (confirmError) {
        throw new Error(confirmError.message);
      }

      if (paymentIntent && paymentIntent.status === 'succeeded') {
        setPaymentStatus('succeeded');
        if (onPaymentSuccess) {
          onPaymentSuccess({
            paymentIntentId: paymentIntent.id,
            amount: paymentIntent.amount,
            status: paymentIntent.status,
            contractId,
          });
        }
      }
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Payment failed';
      setError(errorMessage);
      setPaymentStatus('failed');
      
      if (onPaymentError) {
        onPaymentError(new PaymentError(errorMessage, 'PAYMENT_CONFIRMATION_FAILED'));
      }
    } finally {
      setIsLoading(false);
    }
  };

  const formatAmount = (amount: number, currency: string) => {
    return paymentService.formatAmount(amount * 100, currency);
  };

  if (!stripe || !elements) {
    return (
      <Card className={className}>
        <CardContent className="flex items-center justify-center py-8">
          <div className="text-center">
            <Icon name="Loader2" className="h-8 w-8 animate-spin mx-auto mb-4 text-blue-600" />
            <p className="text-gray-600">Initializing secure payment...</p>
          </div>
        </CardContent>
      </Card>
    );
  }

  if (paymentStatus === 'succeeded') {
    return (
      <Card className={className}>
        <CardContent className="text-center py-8">
          <Icon name="CheckCircle" className="h-16 w-16 text-green-600 mx-auto mb-4" />
          <h3 className="text-2xl font-semibold text-green-600 mb-2">Payment Successful!</h3>
          <p className="text-gray-600 mb-4">
            Your payment of {formatAmount(amount, currency)} has been processed successfully.
          </p>
          <Badge variant="default" className="bg-green-100 text-green-800">
            Payment Confirmed
          </Badge>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card className={className}>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Icon name="CreditCard" className="h-5 w-5" />
          Complete Payment
        </CardTitle>
        <CardDescription>
          Secure payment processing for contract #{contractId.slice(-8)}
        </CardDescription>
      </CardHeader>
      <CardContent>
        <form onSubmit={handleSubmit} className="space-y-6">
          {/* Payment Summary */}
          <div className="bg-gray-50 p-4 rounded-lg">
            <div className="flex justify-between items-center mb-2">
              <span className="text-sm font-medium text-gray-600">Payment Amount:</span>
              <span className="text-lg font-bold text-gray-900">
                {formatAmount(amount, currency)}
              </span>
            </div>
            {clientData && (
              <div className="flex items-center gap-2 text-sm text-gray-600">
                <span>Paying to:</span>
                <div className="flex items-center gap-2">
                  {clientData.profilePhoto && isValidImageUrl(clientData.profilePhoto) ? (
                    <div className="relative w-5 h-5 rounded-full overflow-hidden">
                      <Image 
                        src={clientData.profilePhoto} 
                        alt={clientData.name}
                        width={20}
                        height={20}
                        className="rounded-full object-cover"
                        onError={() => {
                          console.warn('Failed to load profile image:', clientData.profilePhoto);
                        }}
                      />
                    </div>
                  ) : (
                    <div className="w-5 h-5 rounded-full bg-blue-500 text-white text-xs flex items-center justify-center">
                      {clientData.name.charAt(0).toUpperCase()}
                    </div>
                  )}
                  <span className="font-medium">{clientData.name}</span>
                </div>
              </div>
            )}
          </div>

          {/* Error Display */}
          {error && (
            <Alert variant="destructive">
              <Icon name="AlertCircle" className="h-5 w-5 text-red-500" />
              <AlertDescription>{error}</AlertDescription>
            </Alert>
          )}

          {/* Payment Element - Always available in Elements context */}
          <div className="space-y-4">
            {!isElementsReady && (
              <div className="flex items-center justify-center py-8">
                <div className="text-center">
                  <Icon name="Loader2" className="h-6 w-6 animate-spin mx-auto mb-2 text-blue-600" />
                  <p className="text-sm text-gray-600">Loading payment form...</p>
                </div>
              </div>
            )}
            <PaymentElement 
              options={{
                layout: 'tabs',
              }}
              onReady={() => {
                console.log('PaymentElement is ready');
                setIsElementsReady(true);
              }}
              onLoadError={(error) => {
                console.error('PaymentElement load error:', error);
                setError('Failed to load payment form. Please refresh and try again.');
              }}
            />
            
            {/* Security Notice */}
            <div className="flex items-center gap-2 text-sm text-gray-600">
              <Icon name="Shield" className="h-4 w-4" />
              <span>Your payment information is encrypted and secure</span>
            </div>
          </div>

          {/* Action Buttons */}
          <div className="flex gap-3 pt-4">
            <Button
              type="submit"
              disabled={!stripe || !elements || isLoading || !isElementsReady}
              size="lg"
              className="flex-1"
            >
              {isLoading ? (
                <>
                  <Icon name="Loader2" className="mr-2 h-4 w-4 animate-spin" />
                  Processing...
                </>
              ) : !isElementsReady ? (
                <>
                  <Icon name="Loader2" className="mr-2 h-4 w-4 animate-spin" />
                  Loading...
                </>
              ) : (
                <>
                  <Icon name="CreditCard" className="mr-2 h-4 w-4" />
                  Pay {formatAmount(amount, currency)}
                </>
              )}
            </Button>
            
            {onCancel && (
              <Button
                type="button"
                variant="outline"
                onClick={onCancel}
                disabled={isLoading}
                size="lg"
              >
                Cancel
              </Button>
            )}
          </div>
        </form>
      </CardContent>
    </Card>
  );
};

export const CheckoutForm: React.FC<CheckoutFormProps> = (props) => {
  const { stripe: stripeInstance } = useStripeContext();
  const [clientSecret, setClientSecret] = useState<string | null>(null);
  const [isInitializing, setIsInitializing] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [initializationAttempted, setInitializationAttempted] = useState(false);

  const { amount, contractId, clientId, currency = 'usd', clientData, onPaymentError } = props;

  useEffect(() => {
    const initializePayment = async () => {
      if (!stripeInstance) return;

      if (clientSecret || initializationAttempted) return;

      setInitializationAttempted(true);

      try {
        setIsInitializing(true);
        setError(null);

        const validation = paymentService.validatePaymentAmount(amount);
        if (!validation.isValid) {
          console.error('Payment amount validation failed:', validation);
          setError(validation.error!);
          return;
        }

        console.log('Creating payment intent with amount:', amount, 'contractId:', contractId, 'clientId:', clientId);

        // Fetch contract data to get freelancer information
        const contract = await contractService.getContract(contractId);
        if (!contract) {
          throw new Error('Contract not found');
        }

        console.log('Contract data:', {
          id: contract.id,
          freelancerId: contract.freelancerId,
          clientId: contract.clientId
        });

        // Fetch freelancer data to get Stripe account information
        let freelancerStripeAccountId: string | undefined;
        try {
          const freelancer = await userService.getUser(contract.freelancerId);
          if (freelancer.stripeAccountId && freelancer.stripeOnboardingComplete) {
            freelancerStripeAccountId = freelancer.stripeAccountId;
            console.log('Freelancer has Stripe account:', freelancerStripeAccountId);
          } else {
            console.log('Freelancer does not have complete Stripe setup');
          }
        } catch (err) {
          console.warn('Could not fetch freelancer Stripe account:', err);
        }

        const request: CreatePaymentIntentRequest = {
          amount,
          currency,
          contractId,
          clientId,
          freelancerId: contract.freelancerId,
          freelancerStripeAccountId,
          platformFeePercentage: 10.0, // Default platform fee
          metadata: {
            clientName: clientData?.name || 'Unknown Client',
            clientEmail: clientData?.email || '',
            contractTitle: contract.title,
            paymentType: 'contract_payment'
          },
        };

        const response = await paymentService.createPaymentIntent(request);
        console.log('Payment intent response:', response);
        setClientSecret(response.clientSecret);
      } catch (err) {
        const errorMessage = paymentService.getPaymentErrorMessage(err);
        setError(errorMessage);
        if (onPaymentError && err instanceof PaymentError) {
          onPaymentError(err);
        }
      } finally {
        setIsInitializing(false);
      }
    };

    if (stripeInstance && amount > 0 && contractId && clientId && !clientSecret && !initializationAttempted) {
      initializePayment();
    }
  }, [stripeInstance, amount, contractId, clientId, currency, clientData, onPaymentError, clientSecret, initializationAttempted]);

  if (!stripeInstance) {
    return (
      <Card className={props.className}>
        <CardContent className="flex items-center justify-center py-8">
          <div className="text-center">
            <Icon name="Loader2" className="h-8 w-8 animate-spin mx-auto mb-4 text-blue-600" />
            <p className="text-gray-600">Loading payment system...</p>
          </div>
        </CardContent>
      </Card>
    );
  }

  if (isInitializing) {
    return (
      <Card className={props.className}>
        <CardContent className="flex items-center justify-center py-8">
          <div className="text-center">
            <Icon name="Loader2" className="h-8 w-8 animate-spin mx-auto mb-4 text-blue-600" />
            <p className="text-gray-600">Initializing secure payment...</p>
          </div>
        </CardContent>
      </Card>
    );
  }

  if (error) {
    return (
      <Card className={props.className}>
        <CardContent className="flex items-center justify-center py-8">
          <div className="text-center">
            <Alert variant="destructive" className="mb-4">
              <Icon name="AlertCircle" className="h-5 w-5" />
              <AlertDescription>{error}</AlertDescription>
            </Alert>
            <Button variant="outline" onClick={() => window.location.reload()}>
              Try Again
            </Button>
          </div>
        </CardContent>
      </Card>
    );
  }

  if (!clientSecret) {
    return (
      <Card className={props.className}>
        <CardContent className="flex items-center justify-center py-8">
          <div className="text-center">
            <Icon name="AlertCircle" className="h-8 w-8 mx-auto mb-4 text-red-600" />
            <p className="text-gray-600">Failed to initialize payment. Please try again.</p>
          </div>
        </CardContent>
      </Card>
    );
  }

  const elementsOptions = {
    clientSecret,
    appearance: {
      theme: 'stripe' as const,
      variables: {
        colorPrimary: '#0570de',
        colorBackground: '#ffffff',
        colorText: '#30313d',
        colorDanger: '#df1b41',
        fontFamily: 'system-ui, sans-serif',
        spacingUnit: '4px',
        borderRadius: '8px',
      },
    },
  };

  return (
    <Elements stripe={stripeInstance} options={elementsOptions} key={clientSecret}>
      <CheckoutFormInternal {...props} />
    </Elements>
  );
};