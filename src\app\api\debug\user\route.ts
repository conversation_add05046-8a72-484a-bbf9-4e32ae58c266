import { NextRequest, NextResponse } from 'next/server';
import { serverUserService } from '@/api/users/user.service.server';

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const userId = searchParams.get('userId');
    const action = searchParams.get('action') || 'get';

    console.log('Debug user endpoint called:', { userId, action });

    if (action === 'health') {
      // Check DynamoDB connection
      try {
        const healthCheck = await serverUserService.healthCheck();

        return NextResponse.json({
          success: healthCheck.success,
          action: 'health',
          tableName: healthCheck.tableName,
          error: healthCheck.error,
          message: healthCheck.success ? 'DynamoDB connection is working' : 'DynamoDB connection failed'
        });
      } catch (error) {
        console.error('Health check failed:', error);
        return NextResponse.json({
          success: false,
          action: 'health',
          error: error instanceof Error ? error.message : 'Unknown error',
          details: error
        });
      }
    }

    if (action === 'list') {
      // List all users to see what's in the database
      try {
        const result = await serverUserService.listUsers();
        console.log('Users found:', result.items.length);
        
        return NextResponse.json({
          success: true,
          action: 'list',
          totalUsers: result.items.length,
          users: result.items.map(user => ({
            id: user.id,
            name: user.name,
            email: user.email,
            role: user.role,
            createdAt: user.createdAt,
            hasStripeAccount: !!user.stripeAccountId
          })),
          nextToken: undefined // serverUserService doesn't support pagination yet
        });
      } catch (error) {
        console.error('Error listing users:', error);
        return NextResponse.json({
          success: false,
          action: 'list',
          error: error instanceof Error ? error.message : 'Unknown error',
          details: error
        });
      }
    }

    if (!userId) {
      return NextResponse.json(
        {
          error: 'User ID is required for get action',
          usage: 'GET /api/debug/user?userId=<id>&action=get|list|health'
        },
        { status: 400 }
      );
    }

    if (action === 'get') {
      // Try to get specific user
      try {
        console.log('Attempting to get user with ID:', userId);
        const user = await serverUserService.getUser(userId);
        
        console.log('User found:', {
          id: user.id,
          name: user.name,
          email: user.email,
          role: user.role
        });

        return NextResponse.json({
          success: true,
          action: 'get',
          userId: userId,
          user: {
            id: user.id,
            name: user.name,
            email: user.email,
            role: user.role,
            profilePhoto: user.profilePhoto,
            bio: user.bio,
            skills: user.skills,
            createdAt: user.createdAt,
            updatedAt: user.updatedAt,
            // Stripe fields
            stripeAccountId: user.stripeAccountId,
            stripeOnboardingComplete: user.stripeOnboardingComplete,
            stripeAccountStatus: user.stripeAccountStatus,
            stripeChargesEnabled: user.stripeChargesEnabled,
            stripePayoutsEnabled: user.stripePayoutsEnabled,
            stripeDetailsSubmitted: user.stripeDetailsSubmitted
          }
        });
      } catch (error) {
        console.error('Error getting user:', error);
        
        // Check if user was not found
        if (!user) {
          return NextResponse.json({
            success: false,
            action: 'get',
            userId: userId,
            error: 'User not found',
            message: `No user found with ID: ${userId}`,
            suggestion: 'Check if the user exists in the database or if the ID format is correct'
          }, { status: 404 });
        }

        return NextResponse.json({
          success: false,
          action: 'get',
          userId: userId,
          error: error instanceof Error ? error.message : 'Unknown error',
          details: error
        }, { status: 500 });
      }
    }

    return NextResponse.json(
      { 
        error: 'Invalid action',
        validActions: ['get', 'list', 'health'],
        usage: 'GET /api/debug/user?userId=<id>&action=get|list|health'
      },
      { status: 400 }
    );

  } catch (error) {
    console.error('Unexpected error in debug user endpoint:', error);
    return NextResponse.json(
      { 
        error: 'An unexpected error occurred',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { action, userId, userData } = body;

    console.log('Debug user POST endpoint called:', { action, userId });

    if (action === 'create' && userData) {
      // Create a test user
      try {
        const user = await serverUserService.createUser(userData);
        
        return NextResponse.json({
          success: true,
          action: 'create',
          user: user,
          message: 'User created successfully'
        });
      } catch (error) {
        console.error('Error creating user:', error);
        return NextResponse.json({
          success: false,
          action: 'create',
          error: error instanceof Error ? error.message : 'Unknown error',
          details: error
        }, { status: 500 });
      }
    }

    return NextResponse.json(
      { 
        error: 'Invalid action or missing data',
        validActions: ['create'],
        usage: 'POST /api/debug/user with { action: "create", userData: {...} }'
      },
      { status: 400 }
    );

  } catch (error) {
    console.error('Unexpected error in debug user POST endpoint:', error);
    return NextResponse.json(
      { 
        error: 'An unexpected error occurred',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}
