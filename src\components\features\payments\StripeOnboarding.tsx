"use client";

import React, { useState, useEffect, useCallback } from "react";
import { <PERSON>, <PERSON><PERSON>onte<PERSON>, CardHeader, CardTitle } from "@/components/ui/Card";
import { Button } from "@/components/ui/Button";
import { Icon } from "@/components/ui/Icon";
import { useAuth } from "@/lib/auth/AuthContext";
import { stripeConnectApi } from "@/api/stripe/stripe-connect.api";

interface StripeOnboardingProps {
  onComplete?: () => void;
  onError?: (error: string) => void;
}

interface OnboardingStatus {
  hasAccount: boolean;
  accountId?: string;
  status: string;
  currentlyDue?: string[];
  eventuallyDue?: string[];
  chargesEnabled?: boolean;
  payoutsEnabled?: boolean;
  detailsSubmitted?: boolean;
  // Derived properties
  needsOnboarding: boolean;
  onboardingComplete: boolean;
}

export const StripeOnboarding: React.FC<StripeOnboardingProps> = ({
  onComplete,
  onError,
}) => {
  const { user } = useAuth();
  const [loading, setLoading] = useState(false);
  const [status, setStatus] = useState<OnboardingStatus | null>(null);
  const [error, setError] = useState<string | null>(null);

  const checkOnboardingStatus = useCallback(async () => {
    if (!user?.attributes?.sub) return;

    try {
      setLoading(true);
      const statusData = await stripeConnectApi.getOnboardingStatus(
        user?.attributes?.sub
      );
      
      // Map the API response to include derived properties
      const onboardingStatus: OnboardingStatus = {
        ...statusData,
        needsOnboarding: statusData.hasAccount && statusData.status !== 'complete',
        onboardingComplete: statusData.hasAccount && statusData.status === 'complete'
      };
      
      setStatus(onboardingStatus);

      if (onboardingStatus.onboardingComplete && onComplete) {
        onComplete();
      }
    } catch (err) {
      const errorMessage =
        err instanceof Error
          ? err.message
          : "Failed to check onboarding status";
      setError(errorMessage);
      if (onError) {
        onError(errorMessage);
      }
    } finally {
      setLoading(false);
    }
  }, [user?.attributes?.sub, onComplete, onError]);

  useEffect(() => {
    if (user?.attributes?.sub) {
      checkOnboardingStatus();
    }
  }, [user?.attributes?.sub, checkOnboardingStatus]);

  const createStripeAccount = async () => {
    if (!user?.attributes?.sub || !user?.attributes?.email) return;

    try {
      setLoading(true);
      setError(null);

      await stripeConnectApi.createAccount({
        userId: user?.attributes?.sub,
        email: user?.attributes?.email,
        country: "US",
      });

      // Refresh status after account creation
      await checkOnboardingStatus();
    } catch (err) {
      const errorMessage =
        err instanceof Error ? err.message : "Failed to create Stripe account";
      setError(errorMessage);
      if (onError) {
        onError(errorMessage);
      }
    } finally {
      setLoading(false);
    }
  };

  const startOnboarding = async () => {
    if (!user?.attributes?.sub) return;

    try {
      setLoading(true);
      setError(null);

      const returnUrl = `${window.location.origin}/dashboard/payments/onboarding?success=true`;
      const refreshUrl = `${window.location.origin}/dashboard/payments/onboarding?refresh=true`;

      const { url } = await stripeConnectApi.createOnboardingLink({
        userId: user?.attributes?.sub,
        returnUrl,
        refreshUrl,
      });

      // Redirect to Stripe onboarding
      window.location.href = url;
    } catch (err) {
      const errorMessage =
        err instanceof Error ? err.message : "Failed to start onboarding";
      setError(errorMessage);
      if (onError) {
        onError(errorMessage);
      }
    } finally {
      setLoading(false);
    }
  };

  const getStatusIcon = () => {
    if (!status) return "Clock";

    if (status.onboardingComplete) {
      return "CheckCircle";
    } else if (status.hasAccount) {
      return "Clock";
    } else {
      return "XCircle";
    }
  };

  const getStatusColor = () => {
    if (!status) return "text-gray-500";

    if (status.onboardingComplete) {
      return "text-green-600";
    } else if (status.hasAccount) {
      return "text-yellow-600";
    } else {
      return "text-red-600";
    }
  };

  const getStatusText = () => {
    if (!status) return "Checking status...";

    if (status.onboardingComplete) {
      return "Ready to receive payments";
    } else if (status.hasAccount && status.needsOnboarding) {
      return "Complete your account setup";
    } else if (status.hasAccount) {
      return "Account created, pending verification";
    } else {
      return "Account setup required";
    }
  };

  if (loading && !status) {
    return (
      <Card>
        <CardContent className="p-6">
          <div className="flex items-center justify-center">
            <Icon name="Loader" className="h-6 w-6 animate-spin mr-2" />
            <span>Checking payment account status...</span>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center">
          <Icon name="CreditCard" className="h-5 w-5 mr-2" />
          Payment Account Setup
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        {/* Information Section */}
        <div className="bg-blue-50 border border-blue-200 rounded-md p-3">
          <div className="flex">
            <Icon name="Info" className="h-5 w-5 text-blue-400 mr-2 mt-0.5" />
            <div className="text-sm">
              <p className="text-blue-800 font-medium mb-1">
                Payment Setup Process
              </p>
              <p className="text-blue-700 mb-2">
                To receive payments from clients, you need to complete a
                one-time setup with Stripe:
              </p>
              <ul className="text-blue-700 space-y-1 list-disc list-inside">
                <li>Verify your identity with government-issued ID</li>
                <li>Add your bank account for direct deposits</li>
                <li>Provide tax information (W-9 or W-8)</li>
                <li>Complete business information if applicable</li>
              </ul>
              <p className="text-blue-700 mt-2 text-xs">
                This process is secure and handled entirely by Stripe, our
                payment processor.
              </p>
            </div>
          </div>
        </div>

        {error && (
          <div className="bg-red-50 border border-red-200 rounded-md p-3">
            <div className="flex">
              <Icon name="AlertCircle" className="h-5 w-5 text-red-400 mr-2" />
              <span className="text-red-700 text-sm">{error}</span>
            </div>
          </div>
        )}

        <div className="flex items-center space-x-3">
          <Icon
            name={getStatusIcon()}
            className={`h-6 w-6 ${getStatusColor()}`}
          />
          <div>
            <p className="font-medium">{getStatusText()}</p>
            {status && (
              <p className="text-sm text-gray-600">Status: {status.status}</p>
            )}
          </div>
        </div>

        {status && status.onboardingComplete && (
          <div className="bg-green-50 border border-green-200 rounded-md p-3">
            <div className="flex items-center">
              <Icon
                name="CheckCircle"
                className="h-5 w-5 text-green-400 mr-2"
              />
              <div>
                <p className="text-green-800 font-medium">Account Ready!</p>
                <p className="text-green-700 text-sm">
                  You can now receive payments from clients.
                </p>
              </div>
            </div>
          </div>
        )}

        {status && status.currentlyDue && status.currentlyDue.length > 0 && (
          <div className="bg-yellow-50 border border-yellow-200 rounded-md p-3">
            <div className="flex">
              <Icon
                name="AlertTriangle"
                className="h-5 w-5 text-yellow-400 mr-2"
              />
              <div>
                <p className="text-yellow-800 font-medium">Action Required</p>
                <p className="text-yellow-700 text-sm">
                  Please complete the following requirements:
                </p>
                <ul className="text-yellow-700 text-sm mt-1 list-disc list-inside">
                  {status.currentlyDue.map((requirement, index) => (
                    <li key={index}>{requirement.replace(/_/g, " ")}</li>
                  ))}
                </ul>
              </div>
            </div>
          </div>
        )}

        <div className="flex space-x-3">
          {!status?.hasAccount && (
            <Button
              onClick={createStripeAccount}
              disabled={loading}
              className="flex-1"
            >
              {loading ? (
                <>
                  <Icon name="Loader" className="h-4 w-4 animate-spin mr-2" />
                  Creating Account...
                </>
              ) : (
                <>
                  <Icon name="Plus" className="h-4 w-4 mr-2" />
                  Create Payment Account
                </>
              )}
            </Button>
          )}

          {status?.hasAccount && status?.needsOnboarding && (
            <Button
              onClick={startOnboarding}
              disabled={loading}
              className="flex-1"
            >
              {loading ? (
                <>
                  <Icon name="Loader" className="h-4 w-4 animate-spin mr-2" />
                  Starting Setup...
                </>
              ) : (
                <>
                  <Icon name="ExternalLink" className="h-4 w-4 mr-2" />
                  Complete Account Setup
                </>
              )}
            </Button>
          )}

          <Button
            variant="outline"
            onClick={checkOnboardingStatus}
            disabled={loading}
          >
            <Icon name="RefreshCw" className="h-4 w-4 mr-2" />
            Refresh Status
          </Button>
        </div>

        {status?.hasAccount && (
          <div className="text-sm text-gray-600 space-y-1">
            <p>Account Details:</p>
            <ul className="space-y-1 ml-4">
              <li>• Charges enabled: {status.chargesEnabled ? "✓" : "✗"}</li>
              <li>• Payouts enabled: {status.payoutsEnabled ? "✓" : "✗"}</li>
              <li>
                • Details submitted: {status.detailsSubmitted ? "✓" : "✗"}
              </li>
            </ul>
          </div>
        )}
      </CardContent>
    </Card>
  );
};
