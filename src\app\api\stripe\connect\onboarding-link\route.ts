import { NextRequest, NextResponse } from 'next/server';
import { stripe } from '@/lib/stripe';
import { serverUserService } from '@/api/users/user.service.server';
import { StripeAccountStatus } from '@/types/features/payments/payment.types';

const baseUrl = process.env.NEXT_PUBLIC_APP_URL || 'http://localhost:3000';

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const userId = searchParams.get('userId');
    const returnUrl = searchParams.get('returnUrl') || `${baseUrl}/dashboard/payments`;
    const refreshUrl = searchParams.get('refreshUrl') || `${baseUrl}/dashboard/payments?refresh=true`;

    console.log('GET onboarding-link request:', { userId, returnUrl, refreshUrl });

    if (!userId) {
      return NextResponse.json(
        { error: 'User ID is required' },
        { status: 400 }
      );
    }

    // Get user's current onboarding status
    let user;
    try {
      user = await serverUserService.getUser(userId);
    } catch (error) {
      console.error('Error getting user:', error);
      console.error('User ID provided:', userId);
      console.error('User ID type:', typeof userId);
      console.error('User ID length:', userId?.length);

      return NextResponse.json(
        {
          error: 'Failed to retrieve user',
          details: error instanceof Error ? error.message : 'Unknown error'
        },
        { status: 500 }
      );
    }

    // Check if user was found
    if (!user) {
      return NextResponse.json(
        {
          error: 'User not found',
          userId: userId,
          message: `No user found with ID: ${userId}`,
          suggestion: 'Please check if the user exists in the database or verify the user ID format',
          debugEndpoint: `/api/debug/user?userId=${userId}&action=get`
        },
        { status: 404 }
      );
    }

    // If user doesn't have a Stripe account, return status indicating they need to create one first
    if (!user.stripeAccountId) {
      return NextResponse.json({
        hasAccount: false,
        needsOnboarding: true,
        status: 'NOT_STARTED',
        message: 'User needs to create a Stripe account first'
      });
    }

    // Check account status from Stripe
    let account;
    try {
      account = await stripe.accounts.retrieve(user.stripeAccountId);
    } catch (error) {
      console.error('Error retrieving Stripe account:', error);

      // If account doesn't exist in Stripe, clear it from user record
      if (error instanceof Error && error.message.includes('No such account')) {
        try {
          await serverUserService.updateUser({
            id: userId,
            stripeAccountId: null,
            stripeOnboardingComplete: false,
            stripeAccountStatus: null
          });
        } catch (updateError) {
          console.error('Error clearing invalid Stripe account ID:', updateError);
        }

        return NextResponse.json({
          hasAccount: false,
          needsOnboarding: true,
          status: 'NOT_STARTED',
          message: 'Stripe account not found, user needs to create a new one'
        });
      }

      return NextResponse.json(
        {
          error: 'Failed to retrieve Stripe account',
          details: error instanceof Error ? error.message : 'Unknown error'
        },
        { status: 500 }
      );
    }

    const isOnboardingComplete = account.charges_enabled && account.payouts_enabled;

    // Update user status in database
    try {
      await serverUserService.updateUser({
        id: userId,
        stripeOnboardingComplete: isOnboardingComplete,
        stripeChargesEnabled: account.charges_enabled || false,
        stripePayoutsEnabled: account.payouts_enabled || false,
        stripeDetailsSubmitted: account.details_submitted || false,
        stripeAccountStatus: isOnboardingComplete ? StripeAccountStatus.ACTIVE : StripeAccountStatus.PENDING
      });
    } catch (error) {
      console.error('Error updating user Stripe status:', error);
      // Continue even if update fails, as the main functionality should still work
    }

    return NextResponse.json({
      hasAccount: true,
      needsOnboarding: !isOnboardingComplete,
      status: isOnboardingComplete ? 'ACTIVE' : 'PENDING',
      onboardingComplete: isOnboardingComplete,
      chargesEnabled: account.charges_enabled,
      payoutsEnabled: account.payouts_enabled,
      detailsSubmitted: account.details_submitted,
      requirements: account.requirements,
      currentlyDue: account.requirements?.currently_due || [],
      eventuallyDue: account.requirements?.eventually_due || [],
      accountId: account.id
    });

  } catch (error) {
    console.error('Unexpected error in onboarding-link GET:', error);
    return NextResponse.json(
      {
        error: 'An unexpected error occurred',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { userId, returnUrl, refreshUrl } = body;

    console.log('POST onboarding-link request:', { userId, returnUrl, refreshUrl });

    if (!userId) {
      return NextResponse.json(
        { error: 'User ID is required' },
        { status: 400 }
      );
    }

    // Get user's Stripe account
    let user;
    try {
      user = await serverUserService.getUser(userId);
    } catch (error) {
      console.error('Error getting user:', error);
      return NextResponse.json(
        { error: 'User not found' },
        { status: 404 }
      );
    }

    // Check if user was found
    if (!user) {
      return NextResponse.json(
        {
          error: 'User not found',
          userId: userId,
          message: `No user found with ID: ${userId}`,
          suggestion: 'Please check if the user exists in the database or verify the user ID format',
          debugEndpoint: `/api/debug/user?userId=${userId}&action=get`
        },
        { status: 404 }
      );
    }

    if (!user.stripeAccountId) {
      return NextResponse.json(
        { error: 'User does not have a Stripe account. Create account first.' },
        { status: 400 }
      );
    }

    // Create account link for onboarding
    try {
      const accountLink = await stripe.accountLinks.create({
        account: user.stripeAccountId,
        refresh_url: refreshUrl || `${baseUrl}/dashboard/payments?refresh=true`,
        return_url: returnUrl || `${baseUrl}/dashboard/payments`,
        type: 'account_onboarding',
      });

      console.log('Onboarding link created:', accountLink.url);

      // Update user with onboarding URL
      try {
        await serverUserService.updateUser({
          id: userId,
          stripeOnboardingUrl: accountLink.url
        });
      } catch (error) {
        console.error('Error updating user with onboarding URL:', error);
        // Continue even if update fails, as the main functionality should still work
      }

      return NextResponse.json({
        url: accountLink.url,
        expiresAt: accountLink.expires_at
      });

    } catch (error) {
      console.error('Error creating onboarding link:', error);

      if (error instanceof Error && error.message.includes('No such account')) {
        // Clear invalid account ID from user record
        try {
          await serverUserService.updateUser({
            id: userId,
            stripeAccountId: null,
            stripeOnboardingComplete: false,
            stripeAccountStatus: null
          });
        } catch (updateError) {
          console.error('Error clearing invalid Stripe account ID:', updateError);
        }

        return NextResponse.json(
          { error: 'Stripe account not found. Please create a new account first.' },
          { status: 400 }
        );
      }

      return NextResponse.json(
        {
          error: 'Failed to create onboarding link',
          details: error instanceof Error ? error.message : 'Unknown error'
        },
        { status: 500 }
      );
    }

  } catch (error) {
    console.error('Unexpected error in onboarding-link POST:', error);
    return NextResponse.json(
      {
        error: 'An unexpected error occurred',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}
