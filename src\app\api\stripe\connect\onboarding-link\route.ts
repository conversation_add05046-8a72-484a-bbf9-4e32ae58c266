import { NextResponse } from 'next/server';
import { stripe } from '@/lib/stripe';

const baseUrl = process.env.NEXT_PUBLIC_APP_URL || 'http://localhost:3000';

// Helper function to find existing account for a user
async function findExistingAccount(userId: string): Promise<string | null> {
  try {
    const accounts = await stripe.accounts.list({
      limit: 1,
      metadata: { userId }
    });
    
    return accounts.data.length > 0 ? accounts.data[0].id : null;
  } catch (error) {
    console.error('Error finding existing account:', error);
    return null;
  }
}

export async function GET(request: Request) {
  try {
    const { searchParams } = new URL(request.url);
    const userId = searchParams.get('userId');
    const returnUrl = searchParams.get('returnUrl') || `${baseUrl}/dashboard/payments`;
    const refreshUrl = searchParams.get('refreshUrl') || `${baseUrl}/dashboard/payments?refresh=true`;

    if (!userId) {
      return NextResponse.json(
        { error: 'User ID is required' },
        { status: 400 }
      );
    }

    // Check if user already has a Stripe account
    let accountId = await findExistingAccount(userId);
    let isNewAccount = false;

    // If no existing account, create a new one
    if (!accountId) {
      try {
        const account = await stripe.accounts.create({
          type: 'express',
          capabilities: {
            card_payments: { requested: true },
            transfers: { requested: true },
          },
          metadata: {
            userId,
          },
        });
        accountId = account.id;
        isNewAccount = true;
      } catch (error) {
        console.error('Error creating Stripe account:', error);
        return NextResponse.json(
          { 
            error: 'Failed to create Stripe account',
            details: error instanceof Error ? error.message : 'Unknown error'
          },
          { status: 500 }
        );
      }
    }

    try {
      // Create an account link for onboarding
      const accountLink = await stripe.accountLinks.create({
        account: accountId,
        refresh_url: refreshUrl,
        return_url: returnUrl,
        type: 'account_onboarding',
      });

      return NextResponse.json({ 
        url: accountLink.url,
        expiresAt: Math.floor(Date.now() / 1000) + 3600, // 1 hour from now
        accountId,
        isNewAccount
      });
    } catch (error) {
      console.error('Error creating account link:', error);
      return NextResponse.json(
        { 
          error: 'Failed to create Stripe onboarding link',
          details: error instanceof Error ? error.message : 'Unknown error'
        },
        { status: 500 }
      );
    }
  } catch (error) {
    console.error('Unexpected error in onboarding-link:', error);
    return NextResponse.json(
      { 
        error: 'An unexpected error occurred',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}
