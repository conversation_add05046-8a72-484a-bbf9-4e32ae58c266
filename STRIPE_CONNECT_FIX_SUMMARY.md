# Stripe Connect Onboarding Link API Fix

## Problem Summary
The 500 Internal Server Error was occurring when making a GET request to `/api/stripe/connect/onboarding-link?userId=...` with the error message "Failed to create Stripe account".

## Root Causes Identified

### 1. Route File Conflicts
- **Issue**: Two different route handlers existed for the same endpoint path
  - `src/app/api/stripe/connect/onboarding-link/route.ts` (Next.js App Router - correct location)
  - `src/api/stripe/connect/onboarding-link/route.ts` (incorrect location)
- **Solution**: Removed the conflicting route file in `src/api/`

### 2. Missing Create Account Route
- **Issue**: The `stripeConnectApi.createAccount()` function was calling `/api/stripe/connect/create-account`, but this route didn't exist in the App Router structure
- **Solution**: Created `src/app/api/stripe/connect/create-account/route.ts` in the correct location

### 3. Incorrect userService.updateUser() Calls
- **Issue**: The userService.updateUser() function was being called with incorrect parameters:
  ```typescript
  // Incorrect (old code)
  await userService.updateUser(userId, { stripeAccountId: account.id });
  
  // Correct (fixed code)
  await userService.updateUser({ id: userId, stripeAccountId: account.id });
  ```
- **Solution**: Fixed all userService.updateUser() calls to use the correct parameter structure

### 4. Poor Error Handling and Account Management
- **Issue**: The original implementation tried to create Stripe accounts directly in the onboarding-link route without proper checks
- **Solution**: Separated concerns - onboarding-link route now only handles existing accounts, create-account route handles account creation

## Files Modified

### 1. `src/app/api/stripe/connect/onboarding-link/route.ts`
- **Changes**: Complete rewrite with proper error handling
- **Key Improvements**:
  - Proper user service integration
  - Better error handling for missing/invalid Stripe accounts
  - Separation of concerns (no longer creates accounts)
  - Improved logging for debugging

### 2. `src/app/api/stripe/connect/create-account/route.ts` (NEW)
- **Purpose**: Handles Stripe Express account creation
- **Features**:
  - Validates user exists before creating account
  - Proper error handling and cleanup
  - Updates user record with Stripe account information
  - Handles duplicate account creation attempts

### 3. Files Removed
- `src/api/stripe/connect/onboarding-link/route.ts` (conflicting route)
- `src/api/stripe/connect/create-account/route.ts` (wrong location)

## API Flow After Fix

### For New Users (No Stripe Account)
1. `GET /api/stripe/connect/onboarding-link?userId=...`
   - Returns: `{ hasAccount: false, needsOnboarding: true, status: 'NOT_STARTED' }`

2. `POST /api/stripe/connect/create-account`
   - Creates Stripe Express account
   - Updates user record in database
   - Returns: `{ accountId: '...', status: 'created' }`

3. `POST /api/stripe/connect/onboarding-link`
   - Creates onboarding link for existing account
   - Returns: `{ url: 'https://connect.stripe.com/...', expiresAt: ... }`

### For Existing Users (Has Stripe Account)
1. `GET /api/stripe/connect/onboarding-link?userId=...`
   - Checks account status from Stripe
   - Updates user record with latest status
   - Returns current onboarding status

## Error Handling Improvements

### Database Errors
- User not found: Returns 404 with clear error message
- User service errors: Proper error logging and graceful handling

### Stripe API Errors
- Invalid/deleted accounts: Cleans up user record and returns appropriate status
- Stripe API failures: Returns detailed error information for debugging
- Account creation failures: Includes cleanup logic

### General Errors
- All endpoints include comprehensive try-catch blocks
- Detailed logging for debugging
- Graceful degradation when possible

## Testing

### Manual Testing
1. Run the development server: `npm run dev`
2. Use the test script: `node test-stripe-api.js`
3. Test with the actual UI components

### Expected Behavior
- New users can create Stripe accounts successfully
- Existing users can check their onboarding status
- Error messages are clear and actionable
- No more 500 Internal Server Errors

## Frontend Integration

The existing `StripeOnboarding` component should now work correctly:
- Shows "Create Payment Account" button for users without accounts
- Shows "Complete Account Setup" button for users with accounts needing onboarding
- Displays current account status and requirements
- Handles all error states gracefully

## Security Considerations

- All Stripe operations use server-side secret keys
- User validation ensures only authorized users can create/access accounts
- Proper error messages don't expose sensitive information
- Account cleanup prevents orphaned Stripe accounts

## Next Steps

1. Test the fix with real user data
2. Monitor error logs for any remaining issues
3. Consider adding rate limiting for account creation
4. Add comprehensive unit tests for the API endpoints
