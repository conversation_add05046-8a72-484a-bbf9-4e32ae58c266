"use client";

import React, { useState } from 'react';
import { <PERSON>, <PERSON>Content, Card<PERSON><PERSON><PERSON>, CardTitle } from '@/components/ui/Card';
import { Button } from '@/components/ui/Button';
import { Input } from '@/components/ui/Input';
import { Icon } from '@/components/ui/Icon';
import { API_CONFIG } from '@/config/api';

interface TestResult {
  success: boolean;
  status: number;
  data: any;
  error: string | null;
  url: string;
  timestamp: string;
}

export default function SignupDebugPage() {
  const [email, setEmail] = useState('<EMAIL>');
  const [password, setPassword] = useState('TestPassword123!');
  const [name, setName] = useState('Test User');
  const [role, setRole] = useState('FREELANCER');
  const [loading, setLoading] = useState(false);
  const [results, setResults] = useState<TestResult[]>([]);

  const testSignupAPI = async () => {
    setLoading(true);
    
    const signupUrl = API_CONFIG.getUrl(API_CONFIG.AUTH.SIGNUP);
    const timestamp = new Date().toISOString();
    
    try {
      console.log('Testing signup API with URL:', signupUrl);
      
      const response = await fetch(signupUrl, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          email,
          password,
          name,
          role,
        }),
      });

      console.log('Response status:', response.status);
      console.log('Response headers:', Object.fromEntries(response.headers.entries()));

      let data;
      let error = null;

      try {
        const responseText = await response.text();
        console.log('Raw response:', responseText);
        
        if (responseText.trim().startsWith('{') || responseText.trim().startsWith('[')) {
          data = JSON.parse(responseText);
        } else {
          // Response is not JSON (likely HTML error page)
          data = { rawResponse: responseText };
          error = `Expected JSON but received: ${responseText.substring(0, 100)}...`;
        }
      } catch (parseError) {
        console.error('Failed to parse response:', parseError);
        error = `Failed to parse response: ${parseError}`;
        data = null;
      }

      const result: TestResult = {
        success: response.ok,
        status: response.status,
        data,
        error: response.ok ? null : (error || data?.error || `HTTP ${response.status}`),
        url: signupUrl,
        timestamp
      };

      setResults(prev => [result, ...prev]);
      
    } catch (networkError) {
      console.error('Network error:', networkError);
      
      const result: TestResult = {
        success: false,
        status: 0,
        data: null,
        error: networkError instanceof Error ? networkError.message : 'Network error',
        url: signupUrl,
        timestamp
      };

      setResults(prev => [result, ...prev]);
    } finally {
      setLoading(false);
    }
  };

  const testAPIConfig = () => {
    console.log('API Configuration:');
    console.log('BASE_URL:', API_CONFIG.BASE_URL);
    console.log('AUTH.SIGNUP:', API_CONFIG.AUTH.SIGNUP);
    console.log('Generated URL:', API_CONFIG.getUrl(API_CONFIG.AUTH.SIGNUP));
    
    // Test environment variables
    console.log('Environment variables:');
    console.log('NEXT_PUBLIC_API_BASE_URL:', process.env.NEXT_PUBLIC_API_BASE_URL);
    console.log('NODE_ENV:', process.env.NODE_ENV);
  };

  const clearResults = () => {
    setResults([]);
  };

  return (
    <div className="min-h-screen bg-gray-50 py-8">
      <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="text-center mb-8">
          <h1 className="text-3xl font-bold text-gray-900 mb-4">
            Signup API Debug Tool
          </h1>
          <p className="text-lg text-gray-600">
            Test the signup API endpoint to debug authentication issues
          </p>
        </div>

        {/* API Configuration Info */}
        <Card className="mb-6">
          <CardHeader>
            <CardTitle>API Configuration</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
              <div>
                <p><strong>Base URL:</strong> {API_CONFIG.BASE_URL}</p>
                <p><strong>Signup Endpoint:</strong> {API_CONFIG.AUTH.SIGNUP}</p>
                <p><strong>Full URL:</strong> {API_CONFIG.getUrl(API_CONFIG.AUTH.SIGNUP)}</p>
              </div>
              <div>
                <p><strong>Environment:</strong> {process.env.NODE_ENV}</p>
                <p><strong>API Base URL Env:</strong> {process.env.NEXT_PUBLIC_API_BASE_URL || 'Not set'}</p>
              </div>
            </div>
            <Button 
              variant="outline" 
              size="sm" 
              onClick={testAPIConfig}
              className="mt-4"
            >
              <Icon name="Terminal" className="h-4 w-4 mr-2" />
              Log Config to Console
            </Button>
          </CardContent>
        </Card>

        {/* Test Form */}
        <Card className="mb-6">
          <CardHeader>
            <CardTitle>Test Signup Request</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Email
                </label>
                <Input
                  type="email"
                  value={email}
                  onChange={(e) => setEmail(e.target.value)}
                  placeholder="<EMAIL>"
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Password
                </label>
                <Input
                  type="password"
                  value={password}
                  onChange={(e) => setPassword(e.target.value)}
                  placeholder="TestPassword123!"
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Name
                </label>
                <Input
                  type="text"
                  value={name}
                  onChange={(e) => setName(e.target.value)}
                  placeholder="Test User"
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Role
                </label>
                <select
                  value={role}
                  onChange={(e) => setRole(e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                >
                  <option value="FREELANCER">Freelancer</option>
                  <option value="CLIENT">Client</option>
                </select>
              </div>
            </div>
            
            <div className="flex space-x-3">
              <Button 
                onClick={testSignupAPI} 
                disabled={loading}
                className="flex-1"
              >
                {loading ? (
                  <>
                    <Icon name="Loader" className="h-4 w-4 animate-spin mr-2" />
                    Testing...
                  </>
                ) : (
                  <>
                    <Icon name="Play" className="h-4 w-4 mr-2" />
                    Test Signup API
                  </>
                )}
              </Button>
              
              <Button 
                variant="outline" 
                onClick={clearResults}
                disabled={results.length === 0}
              >
                <Icon name="Trash2" className="h-4 w-4 mr-2" />
                Clear Results
              </Button>
            </div>
          </CardContent>
        </Card>

        {/* Results */}
        {results.length > 0 && (
          <Card>
            <CardHeader>
              <CardTitle>Test Results</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {results.map((result, index) => (
                  <div 
                    key={index}
                    className={`p-4 rounded-lg border ${
                      result.success 
                        ? 'border-green-200 bg-green-50' 
                        : 'border-red-200 bg-red-50'
                    }`}
                  >
                    <div className="flex items-center justify-between mb-2">
                      <div className="flex items-center">
                        <Icon 
                          name={result.success ? "CheckCircle" : "XCircle"} 
                          className={`h-5 w-5 mr-2 ${
                            result.success ? 'text-green-600' : 'text-red-600'
                          }`} 
                        />
                        <span className={`font-medium ${
                          result.success ? 'text-green-800' : 'text-red-800'
                        }`}>
                          {result.success ? 'Success' : 'Failed'}
                        </span>
                        <span className="ml-2 text-sm text-gray-600">
                          Status: {result.status}
                        </span>
                      </div>
                      <span className="text-xs text-gray-500">
                        {new Date(result.timestamp).toLocaleTimeString()}
                      </span>
                    </div>
                    
                    <div className="text-sm space-y-2">
                      <p><strong>URL:</strong> {result.url}</p>
                      {result.error && (
                        <p className="text-red-700"><strong>Error:</strong> {result.error}</p>
                      )}
                      {result.data && (
                        <div>
                          <strong>Response Data:</strong>
                          <pre className="mt-1 p-2 bg-gray-100 rounded text-xs overflow-x-auto">
                            {JSON.stringify(result.data, null, 2)}
                          </pre>
                        </div>
                      )}
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        )}

        <div className="mt-8 text-center text-sm text-gray-500">
          <p>
            This debug tool helps identify issues with the signup API endpoint.
            <br />
            Check the browser console for additional debugging information.
          </p>
        </div>
      </div>
    </div>
  );
}
