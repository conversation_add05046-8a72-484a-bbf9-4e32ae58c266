import { NextRequest, NextResponse } from 'next/server';
import <PERSON><PERSON> from 'stripe';
import { userService } from '@/api/users/user.service';
import { StripeAccountStatus } from '@/types/features/payments/payment.types';

const stripe = new Stripe(process.env.STRIPE_SECRET_KEY || '');

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { userId, email, country = 'US' } = body;

    console.log('Creating Stripe Express account for user:', { userId, email, country });

    if (!userId) {
      return NextResponse.json(
        { error: 'User ID is required.' },
        { status: 400 }
      );
    }

    if (!email) {
      return NextResponse.json(
        { error: 'Email is required.' },
        { status: 400 }
      );
    }

    // Check if user already has a Stripe account
    const user = await userService.getUser(userId);
    if (user.stripeAccountId) {
      return NextResponse.json(
        { error: 'User already has a Stripe account.' },
        { status: 400 }
      );
    }

    // Create Stripe Express account
    const account = await stripe.accounts.create({
      type: 'express',
      country: country,
      email: email,
      capabilities: {
        card_payments: { requested: true },
        transfers: { requested: true },
      },
      business_type: 'individual',
      metadata: {
        userId: userId,
        platform: 'myvillage-freelance'
      }
    });

    console.log('Stripe account created:', account.id);

    // Update user with Stripe account information
    await userService.updateUser(userId, {
      stripeAccountId: account.id,
      stripeAccountStatus: 'PENDING',
      stripeAccountType: 'express',
      stripeChargesEnabled: account.charges_enabled || false,
      stripePayoutsEnabled: account.payouts_enabled || false,
      stripeDetailsSubmitted: account.details_submitted || false,
    });

    return NextResponse.json({
      accountId: account.id,
      status: 'created',
      chargesEnabled: account.charges_enabled,
      payoutsEnabled: account.payouts_enabled,
      detailsSubmitted: account.details_submitted
    });

  } catch (error) {
    console.error('Error creating Stripe account:', error);
    
    if (error instanceof Stripe.errors.StripeError) {
      return NextResponse.json(
        { error: error.message, code: error.code },
        { status: 400 }
      );
    }

    return NextResponse.json(
      { error: 'Failed to create Stripe account' },
      { status: 500 }
    );
  }
}

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const userId = searchParams.get('userId');

    if (!userId) {
      return NextResponse.json(
        { error: 'User ID is required.' },
        { status: 400 }
      );
    }

    // Get user's Stripe account information
    const user = await userService.getUser(userId);
    
    if (!user.stripeAccountId) {
      return NextResponse.json({
        hasAccount: false,
        status: 'NOT_STARTED'
      });
    }

    // Get account details from Stripe
    const account = await stripe.accounts.retrieve(user.stripeAccountId);

    // Update user with latest account status
    await userService.updateUser({
      id: userId,
      stripeChargesEnabled: account.charges_enabled || false,
      stripePayoutsEnabled: account.payouts_enabled || false,
      stripeDetailsSubmitted: account.details_submitted || false,
      stripeAccountStatus: account.charges_enabled && account.payouts_enabled ? StripeAccountStatus.ACTIVE : StripeAccountStatus.PENDING
    });

    return NextResponse.json({
      hasAccount: true,
      accountId: account.id,
      status: account.charges_enabled && account.payouts_enabled ? 'ACTIVE' : 'PENDING',
      chargesEnabled: account.charges_enabled,
      payoutsEnabled: account.payouts_enabled,
      detailsSubmitted: account.details_submitted,
      requirements: account.requirements
    });

  } catch (error) {
    console.error('Error retrieving Stripe account:', error);
    
    if (error instanceof Stripe.errors.StripeError) {
      return NextResponse.json(
        { error: error.message, code: error.code },
        { status: 400 }
      );
    }

    return NextResponse.json(
      { error: 'Failed to retrieve Stripe account' },
      { status: 500 }
    );
  }
}
