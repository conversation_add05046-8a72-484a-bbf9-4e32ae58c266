"use client";

import React, { useState, useEffect, useCallback } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/Card';
import { Button } from '@/components/ui/Button';
import { Icon } from '@/components/ui/Icon';
import { useStripe, useElements, CardElement } from '@stripe/react-stripe-js';
import { paymentService } from '@/api/payments/payment.service';
import { userService } from '@/api/users/user.service';

interface PaymentFlowWithCommissionProps {
  contractId: string;
  clientId: string;
  freelancerId: string;
  amount: number;
  currency?: string;
  contractTitle: string;
  platformFeePercentage?: number;
  onSuccess?: (data: any) => void;
  onCancel?: () => void;
  onError?: (error: string) => void;
}

interface PaymentBreakdown {
  totalAmount: number;
  platformFee: number;
  freelancerAmount: number;
  platformFeePercentage: number;
}

export const PaymentFlowWithCommission: React.FC<PaymentFlowWithCommissionProps> = ({
  contractId,
  clientId,
  freelancerId,
  amount,
  currency = 'USD',
  contractTitle,
  platformFeePercentage = 10.0,
  onSuccess,
  onCancel,
  onError
}) => {
  const stripe = useStripe();
  const elements = useElements();
  
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [freelancerStripeAccount, setFreelancerStripeAccount] = useState<string | null>(null);
  const [paymentBreakdown, setPaymentBreakdown] = useState<PaymentBreakdown | null>(null);

  const calculatePaymentBreakdown = useCallback(() => {
    const platformFee = (amount * platformFeePercentage) / 100;
    const freelancerAmount = amount - platformFee;

    setPaymentBreakdown({
      totalAmount: amount,
      platformFee,
      freelancerAmount,
      platformFeePercentage
    });
  }, [amount, platformFeePercentage]);

  const checkFreelancerStripeAccount = useCallback(async () => {
    if (!freelancerId) return;
    
    try {
      const freelancer = await userService.getUser(freelancerId);
      if (freelancer.stripeAccountId && freelancer.stripeOnboardingComplete) {
        setFreelancerStripeAccount(freelancer.stripeAccountId);
      }
    } catch (err) {
      console.error('Error checking freelancer Stripe account:', err);
    }
  }, [freelancerId]);

  useEffect(() => {
    calculatePaymentBreakdown();
    checkFreelancerStripeAccount();
  }, [calculatePaymentBreakdown, checkFreelancerStripeAccount]);

  const handlePayment = async () => {
    if (!stripe || !elements) {
      setError('Stripe has not loaded yet. Please try again.');
      return;
    }

    const cardElement = elements.getElement(CardElement);
    if (!cardElement) {
      setError('Card element not found. Please refresh the page.');
      return;
    }

    try {
      setLoading(true);
      setError(null);

      // Create payment intent with commission split
      const paymentIntentData = await paymentService.createPaymentIntent({
        amount,
        currency: currency.toLowerCase(),
        contractId,
        clientId,
        freelancerId,
        freelancerStripeAccountId: freelancerStripeAccount || undefined,
        platformFeePercentage,
        metadata: {
          contractTitle,
          paymentType: 'contract_payment'
        }
      });

      console.log('Payment intent created:', paymentIntentData);

      // Confirm payment with Stripe
      const { error: confirmError, paymentIntent } = await stripe.confirmCardPayment(
        paymentIntentData.clientSecret,
        {
          payment_method: {
            card: cardElement,
            billing_details: {
              // Add billing details if available
            },
          }
        }
      );

      if (confirmError) {
        throw new Error(confirmError.message);
      }

      if (paymentIntent?.status === 'succeeded') {
        console.log('Payment succeeded:', paymentIntent.id);
        
        if (onSuccess) {
          onSuccess({
            paymentIntentId: paymentIntent.id,
            amount: paymentIntentData.amount,
            platformFeeAmount: paymentIntentData.platformFeeAmount,
            freelancerAmount: paymentIntentData.freelancerAmount,
            hasDestinationCharge: paymentIntentData.hasDestinationCharge,
            contractId,
            status: paymentIntent.status
          });
        }
      } else {
        throw new Error(`Payment failed with status: ${paymentIntent?.status}`);
      }

    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Payment failed';
      setError(errorMessage);
      if (onError) {
        onError(errorMessage);
      }
    } finally {
      setLoading(false);
    }
  };

  const cardElementOptions = {
    style: {
      base: {
        fontSize: '16px',
        color: '#424770',
        '::placeholder': {
          color: '#aab7c4',
        },
      },
      invalid: {
        color: '#9e2146',
      },
    },
  };

  return (
    <Card className="w-full max-w-md mx-auto">
      <CardHeader>
        <CardTitle className="flex items-center">
          <Icon name="CreditCard" className="h-5 w-5 mr-2" />
          Pay for {contractTitle}
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        {/* Payment Breakdown */}
        {paymentBreakdown && (
          <div className="bg-gray-50 rounded-lg p-4 space-y-2">
            <h4 className="font-medium text-gray-900">Payment Breakdown</h4>
            <div className="space-y-1 text-sm">
              <div className="flex justify-between">
                <span>Project Amount:</span>
                <span>${paymentBreakdown.totalAmount.toFixed(2)}</span>
              </div>
              <div className="flex justify-between text-gray-600">
                <span>Platform Fee ({paymentBreakdown.platformFeePercentage}%):</span>
                <span>-${paymentBreakdown.platformFee.toFixed(2)}</span>
              </div>
              <div className="flex justify-between font-medium border-t pt-1">
                <span>Freelancer Receives:</span>
                <span>${paymentBreakdown.freelancerAmount.toFixed(2)}</span>
              </div>
            </div>
          </div>
        )}

        {/* Stripe Account Status */}
        {freelancerStripeAccount ? (
          <div className="bg-green-50 border border-green-200 rounded-md p-3">
            <div className="flex items-center">
              <Icon name="CheckCircle" className="h-4 w-4 text-green-400 mr-2" />
              <span className="text-green-800 text-sm">
                Payment will be automatically transferred to freelancer
              </span>
            </div>
          </div>
        ) : (
          <div className="bg-yellow-50 border border-yellow-200 rounded-md p-3">
            <div className="flex items-center">
              <Icon name="AlertTriangle" className="h-4 w-4 text-yellow-400 mr-2" />
              <span className="text-yellow-800 text-sm">
                Freelancer hasn&apos;t set up payments yet. Funds will be held until setup is complete.
              </span>
            </div>
          </div>
        )}

        {/* Error Display */}
        {error && (
          <div className="bg-red-50 border border-red-200 rounded-md p-3">
            <div className="flex">
              <Icon name="AlertCircle" className="h-4 w-4 text-red-400 mr-2" />
              <span className="text-red-700 text-sm">{error}</span>
            </div>
          </div>
        )}

        {/* Card Input */}
        <div className="space-y-2">
          <label className="block text-sm font-medium text-gray-700">
            Card Details
          </label>
          <div className="border border-gray-300 rounded-md p-3">
            <CardElement options={cardElementOptions} />
          </div>
        </div>

        {/* Action Buttons */}
        <div className="flex space-x-3">
          <Button
            onClick={handlePayment}
            disabled={!stripe || loading}
            className="flex-1"
          >
            {loading ? (
              <>
                <Icon name="Loader" className="h-4 w-4 animate-spin mr-2" />
                Processing...
              </>
            ) : (
              <>
                <Icon name="CreditCard" className="h-4 w-4 mr-2" />
                Pay ${amount.toFixed(2)}
              </>
            )}
          </Button>
          
          {onCancel && (
            <Button
              variant="outline"
              onClick={onCancel}
              disabled={loading}
            >
              Cancel
            </Button>
          )}
        </div>

        {/* Security Notice */}
        <div className="text-xs text-gray-500 text-center">
          <Icon name="Shield" className="h-3 w-3 inline mr-1" />
          Payments are processed securely by Stripe
        </div>
      </CardContent>
    </Card>
  );
};
