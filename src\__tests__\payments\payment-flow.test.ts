/**
 * Payment Flow Integration Tests
 * 
 * These tests demonstrate the complete payment flow including:
 * - Freelancer onboarding
 * - Payment processing with commission splits
 * - Webhook handling
 * - Payment tracking
 */

import { describe, it, expect, beforeEach, jest } from '@jest/globals';
import { stripeConnectApi } from '@/api/stripe/stripe-connect.api';
import { paymentService } from '@/api/payments/payment.service';
import { paymentTrackingService } from '@/api/payments/payment-tracking.service';
import { PaymentStatus, PaymentMethod } from '@/types/features/payments/payment.types';

// Mock external dependencies
jest.mock('@/api/stripe/stripe-connect.api');
jest.mock('@/api/payments/payment.service');
jest.mock('@/api/payments/payment-tracking.service');
jest.mock('@/api/users/user.service');

const mockStripeConnectApi = stripeConnectApi as jest.Mocked<typeof stripeConnectApi>;
const mockPaymentService = paymentService as jest.Mocked<typeof paymentService>;
const mockPaymentTrackingService = paymentTrackingService as jest.Mocked<typeof paymentTrackingService>;

describe('Payment Flow Integration Tests', () => {
  const mockFreelancer = {
    id: 'freelancer_123',
    email: '<EMAIL>',
    name: 'John Freelancer',
    stripeAccountId: null,
    stripeOnboardingComplete: false
  };

  const mockClient = {
    id: 'client_456',
    email: '<EMAIL>',
    name: 'Jane Client'
  };

  const mockContract = {
    id: 'contract_789',
    title: 'Website Development',
    amount: 1500.00,
    clientId: mockClient.id,
    freelancerId: mockFreelancer.id
  };

  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('Freelancer Onboarding Flow', () => {
    it('should create a Stripe Express account for freelancer', async () => {
      // Mock successful account creation
      mockStripeConnectApi.createAccount.mockResolvedValue({
        accountId: 'acct_stripe123',
        status: 'created',
        chargesEnabled: false,
        payoutsEnabled: false,
        detailsSubmitted: false
      });

      const result = await stripeConnectApi.createAccount({
        userId: mockFreelancer.id,
        email: mockFreelancer.email,
        country: 'US'
      });

      expect(mockStripeConnectApi.createAccount).toHaveBeenCalledWith({
        userId: mockFreelancer.id,
        email: mockFreelancer.email,
        country: 'US'
      });

      expect(result).toEqual({
        accountId: 'acct_stripe123',
        status: 'created',
        chargesEnabled: false,
        payoutsEnabled: false,
        detailsSubmitted: false
      });
    });

    it('should generate onboarding link for account setup', async () => {
      mockStripeConnectApi.createOnboardingLink.mockResolvedValue({
        url: 'https://connect.stripe.com/setup/e/acct_stripe123',
        expiresAt: Date.now() + 3600000 // 1 hour from now
      });

      const result = await stripeConnectApi.createOnboardingLink({
        userId: mockFreelancer.id,
        returnUrl: 'https://app.com/onboarding/success',
        refreshUrl: 'https://app.com/onboarding/refresh'
      });

      expect(mockStripeConnectApi.createOnboardingLink).toHaveBeenCalledWith({
        userId: mockFreelancer.id,
        returnUrl: 'https://app.com/onboarding/success',
        refreshUrl: 'https://app.com/onboarding/refresh'
      });

      expect(result.url).toContain('connect.stripe.com');
    });

    it('should check onboarding status and update user', async () => {
      mockStripeConnectApi.getOnboardingStatus.mockResolvedValue({
        hasAccount: true,
        needsOnboarding: false,
        status: 'ACTIVE',
        onboardingComplete: true,
        chargesEnabled: true,
        payoutsEnabled: true,
        detailsSubmitted: true
      });

      const result = await stripeConnectApi.getOnboardingStatus(mockFreelancer.id);

      expect(result.onboardingComplete).toBe(true);
      expect(result.status).toBe('ACTIVE');
    });
  });

  describe('Payment Processing with Commission Splits', () => {
    it('should create payment intent with commission split', async () => {
      const platformFeePercentage = 10.0;
      const totalAmount = mockContract.amount;
      const platformFee = (totalAmount * platformFeePercentage) / 100;
      const freelancerAmount = totalAmount - platformFee;

      mockPaymentService.createPaymentIntent.mockResolvedValue({
        clientSecret: 'pi_test123_secret_test',
        paymentIntentId: 'pi_test123',
        amount: totalAmount,
        currency: 'USD',
        status: 'requires_payment_method',
        platformFeeAmount: platformFee,
        freelancerAmount: freelancerAmount,
        hasDestinationCharge: true
      });

      const result = await paymentService.createPaymentIntent({
        amount: totalAmount,
        currency: 'usd',
        contractId: mockContract.id,
        clientId: mockClient.id,
        freelancerId: mockFreelancer.id,
        freelancerStripeAccountId: 'acct_stripe123',
        platformFeePercentage
      });

      expect(mockPaymentService.createPaymentIntent).toHaveBeenCalledWith({
        amount: totalAmount,
        currency: 'usd',
        contractId: mockContract.id,
        clientId: mockClient.id,
        freelancerId: mockFreelancer.id,
        freelancerStripeAccountId: 'acct_stripe123',
        platformFeePercentage
      });

      expect(result.platformFeeAmount).toBe(150); // 10% of 1500
      expect(result.freelancerAmount).toBe(1350); // 90% of 1500
      expect(result.hasDestinationCharge).toBe(true);
    });

    it('should track payment with commission details', async () => {
      const paymentTrackingData = {
        paymentIntentId: 'pi_test123',
        contractId: mockContract.id,
        clientId: mockClient.id,
        freelancerId: mockFreelancer.id,
        totalAmount: 1500.00,
        platformFeeAmount: 150.00,
        freelancerAmount: 1350.00,
        platformFeePercentage: 10.0,
        currency: 'USD',
        stripeChargeId: 'ch_test123',
        description: 'Payment for Website Development'
      };

      const mockPayment = {
        id: 'payment_123',
        ...paymentTrackingData,
        status: PaymentStatus.PAID,
        method: PaymentMethod.STRIPE,
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString()
      };

      mockPaymentTrackingService.createPaymentRecord.mockResolvedValue(mockPayment);

      const result = await paymentTrackingService.createPaymentRecord(paymentTrackingData);

      expect(mockPaymentTrackingService.createPaymentRecord).toHaveBeenCalledWith(paymentTrackingData);
      expect(result.commissionAmount).toBe(150.00);
      expect(result.freelancerAmount).toBe(1350.00);
      expect(result.status).toBe(PaymentStatus.PAID);
    });
  });

  describe('Payment Analytics and Tracking', () => {
    it('should calculate payment summary with commission data', async () => {
      // Mock the getPaymentsForUser function
      const mockPayments = [
        {
          id: 'payment_1',
          amount: 1500,
          commissionAmount: 150,
          freelancerAmount: 1350,
          status: PaymentStatus.PAID,
          method: PaymentMethod.STRIPE,
          createdAt: new Date().toISOString()
        },
        {
          id: 'payment_2',
          amount: 2000,
          commissionAmount: 200,
          freelancerAmount: 1800,
          status: PaymentStatus.PAID,
          method: PaymentMethod.STRIPE,
          createdAt: new Date().toISOString()
        }
      ];
      
      // Mock the service method
      mockPaymentService.getPaymentsForUser.mockResolvedValue(mockPayments);
      
      // Call the function that uses the payments
      const summary = await paymentTrackingService.getPaymentSummary({
        freelancerId: mockFreelancer.id
      });
      
      // Assert the results
      expect(mockPaymentService.getPaymentsForUser).toHaveBeenCalledWith({
        freelancerId: mockFreelancer.id,
        status: PaymentStatus.PAID
      });
      
      // Verify the summary calculations
      expect(summary.totalVolume).toBe(3500); // 1500 + 2000
      expect(summary.totalCommissions).toBe(350); // 150 + 200
      expect(summary.totalEarnings).toBe(3150); // 1350 + 1800
    });

    it('should get commission earnings for platform analytics', async () => {
      mockPaymentTrackingService.getCommissionEarnings.mockResolvedValue({
        totalCommissions: 350,
        totalVolume: 3500,
        commissionRate: 10.0,
        paymentCount: 2,
        topFreelancers: [
          {
            freelancerId: mockFreelancer.id,
            freelancerName: mockFreelancer.name,
            totalVolume: 3500,
            totalCommissions: 350
          }
        ]
      });

      const earnings = await paymentTrackingService.getCommissionEarnings();

      expect(earnings.totalCommissions).toBe(350);
      expect(earnings.commissionRate).toBe(10.0);
      expect(earnings.topFreelancers).toHaveLength(1);
      expect(earnings.topFreelancers[0].freelancerId).toBe(mockFreelancer.id);
    });
  });

  describe('Error Handling', () => {
    it('should handle Stripe account creation errors', async () => {
      mockStripeConnectApi.createAccount.mockRejectedValue(
        new Error('Invalid email address')
      );

      await expect(
        stripeConnectApi.createAccount({
          userId: mockFreelancer.id,
          email: 'invalid-email',
          country: 'US'
        })
      ).rejects.toThrow('Invalid email address');
    });

    it('should handle payment intent creation errors', async () => {
      mockPaymentService.createPaymentIntent.mockRejectedValue(
        new Error('Insufficient funds')
      );

      await expect(
        paymentService.createPaymentIntent({
          amount: mockContract.amount,
          currency: 'usd',
          contractId: mockContract.id,
          clientId: mockClient.id,
          freelancerId: mockFreelancer.id
        })
      ).rejects.toThrow('Insufficient funds');
    });
  });
});
