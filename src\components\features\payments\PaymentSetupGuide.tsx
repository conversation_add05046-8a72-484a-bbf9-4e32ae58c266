"use client";

import React from 'react';
import { <PERSON>, <PERSON><PERSON><PERSON>nt, CardHeader, CardTitle } from '@/components/ui/Card';
import { Button } from '@/components/ui/Button';
import { Icon } from '@/components/ui/Icon';
import Link from 'next/link';

interface PaymentSetupGuideProps {
  variant?: 'compact' | 'detailed';
  showCTA?: boolean;
  className?: string;
}

export const PaymentSetupGuide: React.FC<PaymentSetupGuideProps> = ({
  variant = 'detailed',
  showCTA = true,
  className = ''
}) => {
  const steps = [
    {
      icon: 'user-check',
      title: 'Identity Verification',
      description: 'Verify your identity with a government-issued ID for security and compliance.'
    },
    {
      icon: 'building-2',
      title: 'Bank Account Setup',
      description: 'Add your bank account details to receive direct deposits from client payments.'
    },
    {
      icon: 'file-text',
      title: 'Tax Information',
      description: 'Provide tax information (W-9 for US residents, W-8 for international) as required by law.'
    },
    {
      icon: 'check-circle',
      title: 'Account Activation',
      description: 'Once verified, your account will be activated and ready to receive payments.'
    }
  ];

  if (variant === 'compact') {
    return (
      <div className={`bg-gradient-to-r from-blue-50 to-indigo-50 border border-blue-200 rounded-lg p-4 ${className}`}>
        <div className="flex items-center justify-between">
          <div className="flex items-center">
            <Icon name="CreditCard" className="h-6 w-6 text-blue-600 mr-3" />
            <div>
              <h3 className="font-medium text-blue-900">Payment Setup Required</h3>
              <p className="text-sm text-blue-700">
                Complete your Stripe account setup to receive payments
              </p>
            </div>
          </div>
          {showCTA && (
            <Link href="/freelancer/payments">
              <Button size="sm" className="bg-blue-600 hover:bg-blue-700">
                <Icon name="ArrowRight" className="h-4 w-4 mr-1" />
                Setup Now
              </Button>
            </Link>
          )}
        </div>
      </div>
    );
  }

  return (
    <Card className={className}>
      <CardHeader>
        <CardTitle className="flex items-center">
          <Icon name="CreditCard" className="h-5 w-5 mr-2" />
          Payment Setup Guide
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-6">
        <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
          <div className="flex items-start">
            <Icon name="Info" className="h-5 w-5 text-blue-600 mr-3 mt-0.5" />
            <div>
              <h4 className="font-medium text-blue-900 mb-1">Why do I need to set up payments?</h4>
              <p className="text-sm text-blue-800">
                To receive payments from clients, you need a verified Stripe account. This ensures secure, 
                direct transfers to your bank account with automatic commission handling.
              </p>
            </div>
          </div>
        </div>

        <div className="space-y-4">
          <h4 className="font-medium text-gray-900">Setup Process:</h4>
          <div className="space-y-3">
            {steps.map((step, index) => (
              <div key={index} className="flex items-start space-x-3">
                <div className="flex-shrink-0 w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center">
                  <span className="text-sm font-medium text-blue-600">{index + 1}</span>
                </div>
                <div className="flex-1">
                  <div className="flex items-center mb-1">
                    <Icon name={step.icon as any} className="h-4 w-4 text-gray-600 mr-2" />
                    <h5 className="font-medium text-gray-900">{step.title}</h5>
                  </div>
                  <p className="text-sm text-gray-600">{step.description}</p>
                </div>
              </div>
            ))}
          </div>
        </div>

        <div className="bg-green-50 border border-green-200 rounded-lg p-4">
          <div className="flex items-start">
            <Icon name="ShieldCheck" className="h-5 w-5 text-green-600 mr-3 mt-0.5" />
            <div>
              <h4 className="font-medium text-green-900 mb-1">Secure & Trusted</h4>
              <p className="text-sm text-green-800">
                All payment processing is handled by Stripe, a trusted payment processor used by millions 
                of businesses worldwide. Your financial information is encrypted and secure.
              </p>
            </div>
          </div>
        </div>

        {showCTA && (
          <div className="flex space-x-3">
            <Link href="/freelancer/payments" className="flex-1">
              <Button className="w-full bg-blue-600 hover:bg-blue-700">
                <Icon name="ArrowRight" className="h-4 w-4 mr-2" />
                Start Payment Setup
              </Button>
            </Link>
            <Button variant="outline" asChild>
              <Link href="https://stripe.com/docs/connect" target="_blank" rel="noopener noreferrer">
                <Icon name="ExternalLink" className="h-4 w-4 mr-2" />
                Learn More
              </Link>
            </Button>
          </div>
        )}

        <div className="text-xs text-gray-500 space-y-1">
          <p>• Setup typically takes 5-10 minutes</p>
          <p>• Account verification may take 1-2 business days</p>
          <p>• No setup fees or monthly charges</p>
          <p>• Standard payment processing fees apply (2.9% + 30¢ per transaction)</p>
        </div>
      </CardContent>
    </Card>
  );
};
