"use client";

import React, { useState, useEffect, useCallback } from "react";
import { Card, Card<PERSON>ontent, CardHeader, CardTitle } from "@/components/ui/Card";
import { Button } from "@/components/ui/Button";
import { Icon } from "@/components/ui/Icon";
import { useAuth } from "@/lib/auth/AuthContext";
import { paymentApi } from "@/api/payments/payment.api";
import { Payment, PaymentStatus } from "@/types/features/payments/payment.types";
import { UserRole } from "@/types/enums";

interface PaymentDashboardProps {
  userRole?: UserRole;
  userId?: string;
}

interface PaymentSummary {
  totalEarnings: number;
  totalPaid: number;
  totalCommissions: number;
  pendingPayments: number;
  completedPayments: number;
}

export const PaymentDashboard: React.FC<PaymentDashboardProps> = ({
  userRole: propUserRole,
  userId: propUserId,
}) => {
  const { user } = useAuth();
  const [payments, setPayments] = useState<Payment[]>([]);
  const [summary, setSummary] = useState<PaymentSummary>({
    totalEarnings: 0,
    totalPaid: 0,
    totalCommissions: 0,
    pendingPayments: 0,
    completedPayments: 0,
  });
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // Use props if provided, otherwise fall back to auth user
  const currentUserId = propUserId || (user as any)?.id || '';
  const currentUserRole = propUserRole || (user as any)?.role || UserRole.CLIENT;

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat("en-US", {
      style: "currency",
      currency: "USD",
    }).format(amount);
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString("en-US", {
      year: "numeric",
      month: "short",
      day: "numeric",
    });
  };

  const getStatusIcon = (status: PaymentStatus) => {
    switch (status) {
      case PaymentStatus.PAID:
      case PaymentStatus.COMPLETED:
        return "CheckCircle";
      case PaymentStatus.PENDING:
      case PaymentStatus.PROCESSING:
        return "Clock";
      case PaymentStatus.FAILED:
        return "XCircle";
      case PaymentStatus.REFUNDED:
        return "RotateCcw";
      default:
        return "HelpCircle";
    }
  };

  const getStatusColor = (status: PaymentStatus) => {
    switch (status) {
      case PaymentStatus.PAID:
      case PaymentStatus.COMPLETED:
        return "text-green-600";
      case PaymentStatus.PENDING:
      case PaymentStatus.PROCESSING:
        return "text-yellow-600";
      case PaymentStatus.FAILED:
        return "text-red-600";
      case PaymentStatus.REFUNDED:
        return "text-blue-600";
      default:
        return "text-gray-600";
    }
  };

  const calculateSummary = useCallback((paymentData: Payment[]) => {
    const newSummary: PaymentSummary = {
      totalEarnings: 0,
      totalPaid: 0,
      totalCommissions: 0,
      pendingPayments: 0,
      completedPayments: 0,
    };

    paymentData.forEach((payment) => {
      if (
        payment.status === PaymentStatus.PAID ||
        payment.status === PaymentStatus.COMPLETED
      ) {
        newSummary.completedPayments++;
        if (currentUserRole === UserRole.FREELANCER) {
          newSummary.totalEarnings += payment.freelancerAmount || payment.amount;
        } else if (currentUserRole === UserRole.CLIENT) {
          newSummary.totalPaid += payment.amount;
        } else if (currentUserRole === UserRole.ADMIN) {
          newSummary.totalCommissions += payment.commissionAmount || 0;
          newSummary.totalPaid += payment.amount;
        }
      } else if (
        payment.status === PaymentStatus.PENDING ||
        payment.status === PaymentStatus.PROCESSING
      ) {
        newSummary.pendingPayments++;
      }
    });

    setSummary(newSummary);
  }, [currentUserRole]);

  const loadPayments = useCallback(async () => {
    if (!currentUserId) return;

    try {
      setLoading(true);
      setError(null);
      const paymentData = await paymentApi.getUserPayments(currentUserId);
      setPayments(paymentData);
      calculateSummary(paymentData);
    } catch (err) {
      const errorMessage =
        err instanceof Error ? err.message : "Failed to load payments";
      setError(errorMessage);
    } finally {
      setLoading(false);
    }
  }, [currentUserId, calculateSummary]);

  useEffect(() => {
    loadPayments();
  }, [loadPayments]);

  if (loading) {
    return (
      <div className="space-y-4">
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          {[1, 2, 3].map((i) => (
            <Card key={i}>
              <CardContent className="p-6">
                <div className="animate-pulse">
                  <div className="h-4 bg-gray-200 rounded w-1/2 mb-2"></div>
                  <div className="h-8 bg-gray-200 rounded w-3/4"></div>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <Card>
        <CardContent className="p-6">
          <div className="text-center">
            <Icon
              name="AlertCircle"
              className="h-12 w-12 text-red-400 mx-auto mb-4"
            />
            <h3 className="text-lg font-medium text-gray-900 mb-2">
              Error Loading Payments
            </h3>
            <p className="text-gray-600 mb-4">{error}</p>
            <Button onClick={loadPayments}>
              <Icon name="RefreshCw" className="h-4 w-4 mr-2" />
              Try Again
            </Button>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <div className="space-y-6">
      {/* Summary Cards */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        {currentUserRole === UserRole.FREELANCER && (
          <Card>
            <CardContent className="p-6">
              <div className="flex items-center">
                <Icon
                  name="DollarSign"
                  className="h-8 w-8 text-green-600 mr-3"
                />
                <div>
                  <p className="text-sm font-medium text-gray-600">
                    Total Earnings
                  </p>
                  <p className="text-2xl font-bold text-gray-900">
                    {formatCurrency(summary.totalEarnings)}
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>
        )}

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center">
              <Icon
                name="CreditCard"
                className="h-8 w-8 text-blue-600 mr-3"
              />
              <div>
                <p className="text-sm font-medium text-gray-600">
                  {currentUserRole === UserRole.FREELANCER
                    ? "Available Balance"
                    : currentUserRole === UserRole.CLIENT
                    ? "Total Spent"
                    : "Total Processed"}
                </p>
                <p className="text-2xl font-bold text-gray-900">
                  {formatCurrency(summary.totalPaid)}
                </p>
              </div>
            </div>
          </CardContent>
        </Card>

        {currentUserRole === UserRole.ADMIN && (
          <Card>
            <CardContent className="p-6">
              <div className="flex items-center">
                <Icon
                  name="TrendingUp"
                  className="h-8 w-8 text-purple-600 mr-3"
                />
                <div>
                  <p className="text-sm font-medium text-gray-600">
                    Total Commissions
                  </p>
                  <p className="text-2xl font-bold text-gray-900">
                    {formatCurrency(summary.totalCommissions)}
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>
        )}
      </div>

      {/* Payment History */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center justify-between">
            <span>Payment History</span>
            <Button variant="outline" size="sm" onClick={loadPayments}>
              <Icon name="RefreshCw" className="h-4 w-4 mr-2" />
              Refresh
            </Button>
          </CardTitle>
        </CardHeader>
        <CardContent>
          {payments.length === 0 ? (
            <div className="text-center py-8">
              <Icon
                name="CreditCard"
                className="h-12 w-12 text-gray-400 mx-auto mb-4"
              />
              <h3 className="text-lg font-medium text-gray-900 mb-2">
                No Payments Yet
              </h3>
              <p className="text-gray-600">
                {currentUserRole === UserRole.FREELANCER
                  ? "You haven't received any payments yet."
                  : "You haven't made any payments yet."}
              </p>
            </div>
          ) : (
            <div className="space-y-4">
              {payments.map((payment) => (
                <div
                  key={payment.id}
                  className="border border-gray-200 rounded-lg p-4"
                >
                  <div className="flex items-center justify-between">
                    <div className="flex items-center space-x-3">
                      <Icon
                        name={getStatusIcon(payment.status)}
                        className={`h-5 w-5 ${getStatusColor(payment.status)}`}
                      />
                      <div>
                        <p className="font-medium text-gray-900">
                          {payment.contract?.title ||
                            `Contract ${payment.contractId}`}
                        </p>
                        <p className="text-sm text-gray-600">
                          {payment.paidAt
                            ? formatDate(payment.paidAt)
                            : formatDate(payment.createdAt)}
                        </p>
                      </div>
                    </div>
                    <div className="text-right">
                      <p className="font-medium text-gray-900">
                        {formatCurrency(
                          currentUserRole === UserRole.FREELANCER
                            ? payment.freelancerAmount || payment.amount
                            : payment.amount
                        )}
                      </p>
                      {currentUserRole === UserRole.ADMIN &&
                        payment.commissionAmount && (
                          <p className="text-sm text-gray-600">
                            Commission:{" "}
                            {formatCurrency(payment.commissionAmount)}
                          </p>
                        )}
                      <p className="text-sm text-gray-600 capitalize">
                        {payment.status.toLowerCase()}
                      </p>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
};
