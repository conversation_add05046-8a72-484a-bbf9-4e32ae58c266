/**
 * Server-side user service that works with DynamoDB directly
 * This is used in API routes where the GraphQL client is not available
 */

import { DynamoDBClient } from '@aws-sdk/client-dynamodb';
import { DynamoDBDocumentClient, GetCommand, PutCommand, UpdateCommand, ScanCommand } from '@aws-sdk/lib-dynamodb';
import { User, CreateUserInput, UpdateUserInput } from '../../types/features/user/user.types';
import { UserRole } from '../../types/enums';
import { StripeAccountStatus } from '../../types/features/payments/payment.types';

// Initialize DynamoDB client
const dynamoClient = new DynamoDBClient({
  region: process.env.AWS_REGION || 'us-east-1',
});

const docClient = DynamoDBDocumentClient.from(dynamoClient);

// Get table name from environment variables
const getUserTableName = () => {
  // Try different environment variable patterns
  const tableName = 
    process.env.API_MYVILLAGEFREELANCE_USERTABLE_NAME ||
    process.env.USER_TABLE_NAME ||
    `User-${process.env.API_MYVILLAGEFREELANCE_GRAPHQLAPIIDOUTPUT}-${process.env.ENV}` ||
    'User-local';
  
  console.log('Using DynamoDB table name:', tableName);
  return tableName;
};

export const serverUserService = {
  /**
   * Get user by ID from DynamoDB
   */
  async getUser(id: string): Promise<User | null> {
    if (!id) {
      throw new Error('User ID is required');
    }

    try {
      const tableName = getUserTableName();
      console.log('Getting user from DynamoDB:', { id, tableName });

      const command = new GetCommand({
        TableName: tableName,
        Key: { id }
      });

      const result = await docClient.send(command);
      
      if (!result.Item) {
        console.log('User not found in DynamoDB:', id);
        return null;
      }

      console.log('User found in DynamoDB:', { 
        id: result.Item.id, 
        name: result.Item.name, 
        email: result.Item.email 
      });

      // Transform DynamoDB item to User type
      const user: User = {
        id: result.Item.id,
        name: result.Item.name || '',
        email: result.Item.email || '',
        role: result.Item.role as UserRole || UserRole.CLIENT,
        profilePhoto: result.Item.profilePhoto || null,
        bio: result.Item.bio || null,
        skills: Array.isArray(result.Item.skills) ? result.Item.skills : [],
        createdAt: result.Item.createdAt || new Date().toISOString(),
        updatedAt: result.Item.updatedAt || new Date().toISOString(),
        // Stripe fields
        stripeAccountId: result.Item.stripeAccountId || null,
        stripeOnboardingComplete: result.Item.stripeOnboardingComplete || false,
        stripeAccountStatus: result.Item.stripeAccountStatus as StripeAccountStatus || null,
        stripeOnboardingUrl: result.Item.stripeOnboardingUrl || null,
        stripeAccountType: result.Item.stripeAccountType || null,
        stripeChargesEnabled: result.Item.stripeChargesEnabled || false,
        stripePayoutsEnabled: result.Item.stripePayoutsEnabled || false,
        stripeDetailsSubmitted: result.Item.stripeDetailsSubmitted || false,
      };

      return user;
    } catch (error) {
      console.error('Error getting user from DynamoDB:', error);
      throw error;
    }
  },

  /**
   * Create user in DynamoDB
   */
  async createUser(input: CreateUserInput): Promise<User> {
    try {
      const tableName = getUserTableName();
      console.log('Creating user in DynamoDB:', { id: input.id, email: input.email, tableName });

      const now = new Date().toISOString();
      const user: User = {
        ...input,
        profilePhoto: input.profilePhoto || null,
        bio: input.bio || null,
        skills: input.skills || [],
        createdAt: now,
        updatedAt: now,
        // Initialize Stripe fields
        stripeAccountId: null,
        stripeOnboardingComplete: false,
        stripeAccountStatus: null,
        stripeOnboardingUrl: null,
        stripeAccountType: null,
        stripeChargesEnabled: false,
        stripePayoutsEnabled: false,
        stripeDetailsSubmitted: false,
      };

      const command = new PutCommand({
        TableName: tableName,
        Item: user,
        ConditionExpression: 'attribute_not_exists(id)' // Prevent overwriting existing users
      });

      await docClient.send(command);
      
      console.log('User created successfully in DynamoDB:', user.id);
      return user;
    } catch (error) {
      console.error('Error creating user in DynamoDB:', error);
      throw error;
    }
  },

  /**
   * Update user in DynamoDB
   */
  async updateUser(input: UpdateUserInput): Promise<User> {
    try {
      const tableName = getUserTableName();
      console.log('Updating user in DynamoDB:', { id: input.id, tableName });

      // First get the current user to merge with updates
      const currentUser = await this.getUser(input.id);
      if (!currentUser) {
        throw new Error(`User not found: ${input.id}`);
      }

      // Merge current user with updates
      const updatedUser: User = {
        ...currentUser,
        ...input,
        updatedAt: new Date().toISOString(),
      };

      const command = new PutCommand({
        TableName: tableName,
        Item: updatedUser
      });

      await docClient.send(command);
      
      console.log('User updated successfully in DynamoDB:', updatedUser.id);
      return updatedUser;
    } catch (error) {
      console.error('Error updating user in DynamoDB:', error);
      throw error;
    }
  },

  /**
   * List all users from DynamoDB
   */
  async listUsers(limit: number = 50): Promise<{ items: User[]; count: number }> {
    try {
      const tableName = getUserTableName();
      console.log('Listing users from DynamoDB:', { tableName, limit });

      const command = new ScanCommand({
        TableName: tableName,
        Limit: limit
      });

      const result = await docClient.send(command);
      
      const items = (result.Items || []).map(item => ({
        id: item.id,
        name: item.name || '',
        email: item.email || '',
        role: item.role as UserRole || UserRole.CLIENT,
        profilePhoto: item.profilePhoto || null,
        bio: item.bio || null,
        skills: Array.isArray(item.skills) ? item.skills : [],
        createdAt: item.createdAt || new Date().toISOString(),
        updatedAt: item.updatedAt || new Date().toISOString(),
        // Stripe fields
        stripeAccountId: item.stripeAccountId || null,
        stripeOnboardingComplete: item.stripeOnboardingComplete || false,
        stripeAccountStatus: item.stripeAccountStatus as StripeAccountStatus || null,
        stripeOnboardingUrl: item.stripeOnboardingUrl || null,
        stripeAccountType: item.stripeAccountType || null,
        stripeChargesEnabled: item.stripeChargesEnabled || false,
        stripePayoutsEnabled: item.stripePayoutsEnabled || false,
        stripeDetailsSubmitted: item.stripeDetailsSubmitted || false,
      })) as User[];

      console.log('Users listed from DynamoDB:', { count: items.length });
      return { items, count: items.length };
    } catch (error) {
      console.error('Error listing users from DynamoDB:', error);
      throw error;
    }
  },

  /**
   * Check if DynamoDB connection is working
   */
  async healthCheck(): Promise<{ success: boolean; tableName: string; error?: string }> {
    try {
      const tableName = getUserTableName();
      
      // Try to scan with limit 1 to test connection
      const command = new ScanCommand({
        TableName: tableName,
        Limit: 1
      });

      await docClient.send(command);
      
      return { success: true, tableName };
    } catch (error) {
      console.error('DynamoDB health check failed:', error);
      return { 
        success: false, 
        tableName: getUserTableName(),
        error: error instanceof Error ? error.message : 'Unknown error'
      };
    }
  }
};
