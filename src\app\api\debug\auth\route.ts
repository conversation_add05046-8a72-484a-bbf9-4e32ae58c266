import { NextRequest, NextResponse } from 'next/server';

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const userId = searchParams.get('userId');

    console.log('Debug auth endpoint called with userId:', userId);

    // This endpoint is for debugging authentication context
    // Since we're on the server side, we can't access the client-side auth context
    // But we can provide information about what we expect

    const debugInfo = {
      serverSide: true,
      message: 'This endpoint runs on the server and cannot access client-side auth context',
      expectedUserIdFormat: 'UUID format like: 04a824f8-b0e1-70ce-2aba-b5abff83b7f0',
      receivedUserId: userId,
      userIdAnalysis: {
        provided: !!userId,
        length: userId?.length || 0,
        format: userId ? (
          /^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i.test(userId) 
            ? 'Valid UUID format' 
            : 'Invalid UUID format'
        ) : 'No userId provided',
        isValidUUID: userId ? /^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i.test(userId) : false
      },
      troubleshootingSteps: [
        '1. Check if user is properly authenticated in the frontend',
        '2. Verify user.attributes.sub is populated in AuthContext',
        '3. Check if user exists in the database using /api/debug/user?action=list',
        '4. Verify the user ID format matches what\'s stored in the database',
        '5. Check if the user was created during the signup process'
      ],
      nextSteps: [
        'Use /api/debug/user?action=list to see all users in database',
        'Use /api/debug/user?userId=<id>&action=get to check specific user',
        'Check browser console for authentication context details',
        'Verify AWS Cognito user attributes include sub field'
      ]
    };

    return NextResponse.json(debugInfo);

  } catch (error) {
    console.error('Error in debug auth endpoint:', error);
    return NextResponse.json(
      { 
        error: 'An unexpected error occurred',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}
